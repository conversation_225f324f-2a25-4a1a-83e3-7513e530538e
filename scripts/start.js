/* eslint-disable import/no-commonjs */
const inquirer = require('inquirer')
const shell = require('shelljs')

inquirer
  .prompt([
    {
      type: 'list',
      name: 'platform',
      message: '请选择平台:',
      choices: [
        'weapp',
        'swan',
        'alipay',
        'tt',
        'h5',
        'rn',
        'qq',
        'jd',
        'quickapp',
      ],
    },
    {
      type: 'list',
      name: 'apiEnv',
      message: '请选择后端接口环境:',
      choices: ['dev', 'production','mock'],
    },
    {
      type: 'list',
      name: 'nodeEnv',
      message: '请选择编译环境:',
      choices: ['development', 'production'],
    },
    {
      type: 'confirm',
      name: 'useMock',
      message: '是否启用 Mock 数据?',
      default: false,
    },
  ])
  .then((answers) => {
    const cli = 'C:\\"Program Files (x86)"\\Tencent\\微信web开发者工具\\cli.bat'
    shell.exec(`${cli} open --project ${process.cwd()}`)
    const { platform, apiEnv, nodeEnv, useMock } = answers

    // 构建启动命令
    let command = `npx cross-env OUTPUT=dist NODE_ENV=${nodeEnv}`

    // 如果启用 Mock，使用 mock 作为 API_ENV
    if (useMock) {
      command += ` API_ENV=mock taro build --type ${platform} --watch`
      console.log('🎭 Mock 模式已启用，接口将使用本地 Mock 数据')

      // 启动 Mock 服务器
      console.log('🚀 正在启动 Mock 服务器...')
      shell.exec('cd mock && npm install', { silent: true })
      shell.exec('cd mock && npm start &', { async: true })

      // 等待 Mock 服务器启动
      setTimeout(() => {
        console.log('🔨 正在启动 Taro 构建...')
        shell.exec(command)
      }, 2000)
    } else {
      command += ` API_ENV=${apiEnv} taro build --type ${platform} --watch`
      console.log('🌐 使用真实接口环境:', apiEnv)
      console.log('🔨 正在启动 Taro 构建...')
      shell.exec(command)
    }
  })
  .catch((err) => {
    console.log('启动出错:', err)
  })

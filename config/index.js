import * as path from "path";

const config = {
  projectName: "pos_suitsupply",
  date: "2025-7-07",
  designWidth(input) {
    return 375; // 设计稿宽度为375px
  },
  deviceRatio: {
    640: 2.34 / 2,
    750: 1,
    828: 1.81 / 2,
    375: 2 // 375px设计稿：1px = 2rpx
  },
  alias: {
    "@": path.resolve(__dirname, "../src")
  },
  sourceRoot: "src",
  outputRoot: "dist",
  plugins: [
    "@tarojs/plugin-html",
    [
      "@tarojs/plugin-inject",
      {
        // 配置需要新增的 API
        syncApis: ["getPrivacySetting", "openPrivacyContract"],
        components: {
          // 配置组件新增 属性和事件
          Button: {
            bindAgreePrivacyAuthorization: ""
          }
        }
      }
    ]
  ],
  env: {
    API_ENV: JSON.stringify(process.env.API_ENV || "production")
  },
  defineConstants: {},
  framework: "react",
  compiler: {
    type: "webpack5",
    prebundle: { enable: false } //会报错
  },
  cache: {
    enable: false // Webpack 持久化缓存配置，建议开启。默认配置请参考：https://docs.taro.zone/docs/config-detail#cache
  },
  sass: {
    projectDirectory: path.resolve(__dirname, ".."),
    resource: []
  },
  mini: {
    hot: true,
    postcss: {
      pxtransform: {
        enable: true, // 启用 px 转换为 rpx
        config: {
          platform: "weapp",
          designWidth: 375, // 设计稿宽度
          deviceRatio: {
            375: 2, // 375px设计稿：1px = 2rpx
            750: 1 // 兼容750px设计稿
          }
        }
      },
      url: {
        enable: true,
        config: {
          limit: 1024 // 设定转换尺寸上限
        }
      },
      cssModules: {
        enable: true, // 默认为 false，如需使用 css modules 功能，则设为 true
        config: {
          namingPattern: "module", // 转换模式，取值为 global/module
          generateScopedName: "[name]__[local]___[hash:base64:5]"
        }
      },
      // 添加 Tailwind CSS 支持
      tailwindcss: {
        enable: true,
        config: {}
      },
      autoprefixer: {
        enable: true,
        config: {}
      }
    },
    miniCssExtractPluginOption: {
      ignoreOrder: true
    },
    optimizeMainPackage: {
      enable: true
    }
  },
  h5: {
    publicPath: "/",
    staticDirectory: "static",
    postcss: {
      autoprefixer: {
        enable: true,
        config: {}
      },
      cssModules: {
        enable: false, // 默认为 false，如需使用 css modules 功能，则设为 true
        config: {
          namingPattern: "module", // 转换模式，取值为 global/module
          generateScopedName: "[name]__[local]___[hash:base64:5]"
        }
      },
      // 添加 Tailwind CSS 支持
      tailwindcss: {
        enable: true,
        config: {}
      }
    }
  },

  
};

module.exports = function (merge) {
  if (process.env.NODE_ENV === "development") {
    return merge({}, config, require("./dev"));
  }
  if (process.env.NODE_ENV === "uat") {
    return merge({}, config, require("./uat"));
  }
	if (process.env.NODE_ENV === "mock") {
    return merge({}, config, require("./mock"));
  }
  return merge({}, config, require("./prod"));
};

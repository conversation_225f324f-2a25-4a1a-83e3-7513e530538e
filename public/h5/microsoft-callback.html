<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>微软登录处理中...</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 0;
            background: #f5f5f5;
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
        }
        
        .container {
            background: white;
            border-radius: 12px;
            padding: 48px 32px;
            text-align: center;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            max-width: 400px;
            width: 90%;
        }
        
        .status-icon {
            font-size: 48px;
            margin-bottom: 16px;
        }
        
        .status-title {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 12px;
            color: #0078d4;
        }
        
        .status-message {
            font-size: 16px;
            color: #666;
            margin-bottom: 24px;
            line-height: 1.5;
        }
        
        .loading-spinner {
            display: inline-block;
            width: 32px;
            height: 32px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #0078d4;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .error {
            color: #d13438;
        }
        
        .success {
            color: #107c10;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="status-icon" id="statusIcon">⏳</div>
        <div class="status-title" id="statusTitle">正在处理登录...</div>
        <div class="status-message" id="statusMessage">请稍候，正在验证您的身份信息</div>
        <div class="loading-spinner" id="loadingSpinner"></div>
    </div>

    <script>
        // 微软OAuth回调处理脚本
        (function() {
            const statusIcon = document.getElementById('statusIcon');
            const statusTitle = document.getElementById('statusTitle');
            const statusMessage = document.getElementById('statusMessage');
            const loadingSpinner = document.getElementById('loadingSpinner');

            // 更新状态显示
            function updateStatus(type, title, message, icon) {
                statusIcon.textContent = icon;
                statusTitle.textContent = title;
                statusMessage.textContent = message;
                
                if (type === 'error') {
                    statusTitle.className = 'status-title error';
                    loadingSpinner.style.display = 'none';
                } else if (type === 'success') {
                    statusTitle.className = 'status-title success';
                    loadingSpinner.style.display = 'none';
                }
            }

            // 解析URL参数
            function parseUrlParams() {
                const params = new URLSearchParams(window.location.search);
                return {
                    code: params.get('code'),
                    state: params.get('state'),
                    error: params.get('error'),
                    error_description: params.get('error_description')
                };
            }

            // 向小程序发送消息
            function postMessageToMiniProgram(type, payload) {
                if (window.wx && window.wx.miniProgram) {
                    window.wx.miniProgram.postMessage({
                        data: { type, payload }
                    });
                    
                    // 通知小程序页面更新
                    window.wx.miniProgram.navigateBack();
                } else {
                    console.log('不在小程序环境中，消息:', { type, payload });
                }
            }

            // 处理授权码并传递给小程序
            function handleAuthorizationCode(code, state) {
                try {
                    updateStatus('loading', '正在验证授权信息...', '请稍候', '🔄');

                    // 验证state参数（从localStorage获取）
                    const storedState = localStorage.getItem('oauth_state');
                    if (!storedState || storedState !== state) {
                        throw new Error('State验证失败，可能存在安全风险');
                    }

                    // 获取PKCE验证码
                    const codeVerifier = localStorage.getItem('oauth_code_verifier');

                    updateStatus('success', '登录成功', '正在跳转...', '✅');

                    // 清理临时数据
                    localStorage.removeItem('oauth_state');
                    localStorage.removeItem('oauth_nonce');
                    localStorage.removeItem('oauth_code_verifier');

                    // 发送授权码到小程序，由后端处理令牌交换
                    postMessageToMiniProgram('MICROSOFT_LOGIN_SUCCESS', {
                        authorizationCode: code,
                        codeVerifier: codeVerifier,
                        redirectUri: window.location.href.split('?')[0]
                    });

                } catch (error) {
                    console.error('处理微软登录失败:', error);
                    updateStatus('error', '登录失败', error.message, '❌');

                    // 发送错误消息到小程序
                    postMessageToMiniProgram('MICROSOFT_LOGIN_ERROR', {
                        message: error.message
                    });
                }
            }

            // 主处理逻辑
            function handleCallback() {
                const params = parseUrlParams();

                if (params.error) {
                    updateStatus('error', '登录失败', params.error_description || params.error, '❌');
                    postMessageToMiniProgram('MICROSOFT_LOGIN_ERROR', {
                        message: params.error_description || params.error
                    });
                    return;
                }

                if (params.code && params.state) {
                    handleAuthorizationCode(params.code, params.state);
                } else {
                    updateStatus('error', '登录失败', '未收到有效的授权码', '❌');
                    postMessageToMiniProgram('MICROSOFT_LOGIN_ERROR', {
                        message: '未收到有效的授权码'
                    });
                }
            }

            // 页面加载完成后开始处理
            if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', handleCallback);
            } else {
                handleCallback();
            }
        })();
    </script>
</body>
</html>

# ShoppingBag Store 实现总结

## 完成的工作

根据 `mock/order.js` 中的接口数据字段，我已经完成了一个功能完整的购物车状态管理 store。

### 1. 核心文件创建/修改

#### 主要实现文件
- **`src/mobx/store/mall.ts`** - 购物车 Store 主要实现
- **`src/mobx/index.ts`** - 添加 shoppingBagStore 导出
- **`mock/order.js`** - 添加购物车相关 Mock 接口
- **`src/http/order/index.ts`** - 添加购物车 API 接口定义

#### 文档和示例
- **`docs/ShoppingBagStore.md`** - 详细使用文档
- **`src/examples/ShoppingBagExample.tsx`** - 使用示例组件
- **`src/tests/shoppingBagStore.test.ts`** - 测试文件

### 2. 实现的功能特性

#### ✅ 商品管理
- **添加商品** (`addToCart`) - 支持库存校验，相同商品自动合并数量
- **删除商品** (`removeItem`) - 根据 cartItemId 删除指定商品
- **修改数量** (`updateQuantity`) - 支持库存校验，数量为0时自动删除
- **查询商品** - 支持按条码、商品编号查找

#### ✅ 库存校验
- 添加商品时自动调用 `getGoodsSizeList` 接口校验库存
- 更新数量时重新校验库存
- 库存不足时阻止操作并显示提示

#### ✅ 单行商品打折
- `setItemDiscount` 方法支持设置 0-1 之间的折扣值
- 自动计算折扣价格和小计金额
- 支持显示原价和折扣价

#### ✅ 金额计算
- 每次购物车变动自动调用后端接口 `/web/mobile/default.php?c=cart&m=calculate`
- 支持本地计算作为 fallback
- 计算结果包括：总数量、总金额、折扣金额、最终金额

#### ✅ 选择管理
- 单个商品选中/取消选中
- 全选/取消全选功能
- 只计算选中商品的金额

#### ✅ 数据持久化
- 使用 `mobx-persist-store` 实现数据持久化
- 购物车数据在应用重启后保持

### 3. 数据结构设计

#### ShoppingCartItem 接口
基于 `mock/order.js` 中的 `OrderGoods` 接口扩展：

```typescript
interface ShoppingCartItem extends OrderGoods {
  quantity: number;        // 商品数量
  selected: boolean;       // 是否选中
  discount?: number;       // 单行商品折扣 (0-1)
  discountPrice?: number;  // 折扣后价格
  subtotal: number;        // 小计金额
  addTime: string;         // 添加时间
  cartItemId: string;      // 购物车项目唯一ID
}
```

#### 计算结果接口
```typescript
interface CartCalculation {
  totalQuantity: number;   // 总数量
  totalAmount: number;     // 总金额
  discountAmount: number;  // 折扣金额
  finalAmount: number;     // 最终金额
}
```

### 4. 新增的 Mock 接口

#### 购物车金额计算接口
- **URL**: `POST /web/mobile/default.php?c=cart&m=calculate`
- **功能**: 根据商品列表计算总金额、折扣等
- **参数**: `{ items: [{ goods_sn, barcode, quantity, price, discount }] }`

#### 购物车库存校验接口
- **URL**: `POST /web/mobile/default.php?c=cart&m=validate_stock`
- **功能**: 批量校验商品库存
- **参数**: `{ items: [{ barcode, quantity }] }`

### 5. 主要方法说明

#### 商品操作方法
```typescript
// 添加商品（自动库存校验）
await shoppingBagStore.addToCart(product, quantity);

// 更新数量（自动库存校验）
await shoppingBagStore.updateQuantity(cartItemId, newQuantity);

// 移除商品
shoppingBagStore.removeItem(cartItemId);

// 设置折扣（0.8 = 8折）
shoppingBagStore.setItemDiscount(cartItemId, 0.8);

// 切换选中状态
shoppingBagStore.toggleItemSelection(cartItemId);

// 全选/取消全选
shoppingBagStore.toggleSelectAll(true);

// 清空购物车
shoppingBagStore.clearCart();
```

#### 查询方法
```typescript
// 根据条码查找商品
const item = shoppingBagStore.findItemByBarcode(barcode);

// 获取商品数量
const quantity = shoppingBagStore.getItemQuantity(barcode);

// 检查是否在购物车
const inCart = shoppingBagStore.isItemInCart(barcode);

// 获取购物车摘要
const summary = shoppingBagStore.getCartSummary();
```

#### 计算属性
```typescript
// 选中商品总数量
shoppingBagStore.totalQuantity

// 选中商品列表
shoppingBagStore.selectedItems

// 是否为空
shoppingBagStore.isEmpty

// 最后计算结果
shoppingBagStore.lastCalculation
```

### 6. 使用方式

```typescript
import { observer } from "mobx-react";
import { shoppingBagStore } from "@/mobx";

const MyComponent = observer(() => {
  return (
    <div>
      <div>总数量: {shoppingBagStore.totalQuantity}</div>
      <div>总金额: ¥{shoppingBagStore.lastCalculation.finalAmount}</div>
      
      {shoppingBagStore.cartItems.map(item => (
        <div key={item.cartItemId}>
          {item.goods_name} - 数量: {item.quantity}
        </div>
      ))}
    </div>
  );
});
```

### 7. 特色功能

1. **智能库存校验** - 添加/更新商品时自动校验库存
2. **灵活折扣系统** - 支持单行商品独立设置折扣
3. **实时金额计算** - 每次变动自动调用后端接口计算
4. **数据持久化** - 购物车数据自动保存到本地
5. **完整的增删改查** - 提供丰富的商品操作和查询方法
6. **类型安全** - 完整的 TypeScript 类型定义

### 8. 注意事项

1. 商品数据结构完全基于 `mock/order.js` 中的接口字段
2. 库存校验使用现有的 `getGoodsSizeList` 接口
3. 金额计算优先使用后端接口，失败时使用本地计算
4. 所有异步操作都有错误处理和用户提示
5. 支持相同商品（相同 barcode）的数量合并

这个实现完全满足了您的需求：购物车商品存储在 store 中，支持增删改查，添加时校验库存，支持单行打折，每次变动重新计算金额。

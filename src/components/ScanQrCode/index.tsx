import { View } from "@tarojs/components";

import IconFont from "@/components/iconfont/index.weapp";
import "./index.scss";

interface ScanQrCodeProps {
  onClick?: () => void;
}

// 定义一个名为ScanQrCode的函数组件
const ScanQrCode = (props) => {
  return (
    <View
      className="com_movableview_scan"
      onClick={() => {
        props.onScanBtnClick();
      }}>
      <IconFont name="scan" size={46} color="#ffffff" />
    </View>
  );
};

export default ScanQrCode;

import { View, Text } from "@tarojs/components";
import { Image, Divider } from "@nutui/nutui-react-taro";
import IconFont from "@/components/iconfont";
import { toJump } from "@/utils";
import { useStores } from "@/hook";
import { useI18n } from "@/hook";
interface infoType {
  avatar: string;
  name: string;
  mobile: string;
  email: string;
}
interface CustomerCardType {
  info: infoType;
}
const CustomerCard: React.FC<CustomerCardType> = ({ info }) => {
  const { customerStore } = useStores();

  const { currentCustomer } = customerStore;
  const { t } = useI18n();

  return (
    <>
      {info ? (
        <>
          {/* 没有current 但是info有内容(用户详情) */}
          <View className="customerCardCom p-8 border-1-area-xs flex">
            <View className="size-24 mr-5">
              {info.avatar ? (
                <Image radius={"50%"} src={info.avatar} />
              ) : (
                <IconFont name="avatar" size={48} />
              )}
            </View>
            <View>
              <View className="leading-10 text-base font-medium text-brand-2D2E2C flex items-center">
                <View>{info.name}</View>
                <View
                  className="ml-2"
                  onClick={() => {
                    toJump("subpackages/customer/edit/index");
                  }}
                >
                  <IconFont name="bianji" size={16} />
                </View>
              </View>
              <View className="mt-4 text-sm leading-8 text-brand-707070">
                Phone Number: {info.mobile}
              </View>
              <View className="mt-2 text-sm leading-8 text-brand-707070">
                Email: {info.email}
              </View>
            </View>
          </View>
          {/*  有currentCustomer时才显示 */}
          {customerStore.currentCustomer?.uid && (
            <>
              <Divider className="mt-2 mb-10 px-8 box-border " />
              <View
                className="flex items-center justify-between px-8 pb-8"
                onClick={() => {
                  toJump("subpackages/customer/index/index");
                }}
              >
                <View className="text-base leading-10 text-brand-2D2E2C">
                  {t("customer.switchCustomer")}
                </View>
                <View className="flex items-center text-sm leading-8 text-brand-2D2E2C ">
                  <View>{t("customer.view")}</View>
                  <IconFont name="detail" size={20} />
                </View>
              </View>
            </>
          )}
        </>
      ) : (
        <View className="customerCardCom m-8 p-8 border-1-area-xs flex-between bg-brand-FAFAFA border-1-area">
          <View
            className="mr-5 flex-center text-md leading-10"
            onClick={() => {
              toJump("subpackages/customer/index/index");
            }}
          >
            <Text className=" font-gt-america-medium ">
              Customer not registered 
            </Text>
            <IconFont name="detail" size={24} />
          </View>
          <View>
            {info.avatar ? (
              <Image radius={"50%"} src={info.avatar} />
            ) : (
              <IconFont name="avatar" size={48} />
            )}
          </View>
        </View>
      )}
    </>
  );
};

export default CustomerCard;

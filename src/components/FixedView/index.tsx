import { ZIndex } from '@/constants/zIndex'
import { useUUIDSelect } from '@/hook'
import { View } from '@tarojs/components'
import classNames from 'classnames'
import { throttle } from 'lodash'
import { observer } from 'mobx-react'
import { ReactNode, useCallback, useEffect, useState } from 'react'
import './index.scss'

export interface FixedViewProps {
  children: ReactNode
  className?: string
  position?: 'top' | 'bottom' | 'center'
  placeholder?: boolean
  style?: any
  //高阶全屏zindex 9990 高层组件 HeaderBar
  highComp?: boolean
  zIndex?: ZIndex
  [key: string]: any
}

const FixedView: React.FC<FixedViewProps> = (props) => {
  const [height, setHeight] = useState<number>(0)
  const { uuid, select } = useUUIDSelect()

  const { children, className, position, placeholder, style, highComp, zIndex, ...restProps } =
    props
  const watchResize = useCallback(
    throttle(() => {
      select().then((res) => {
        if (res) {
          //res不存在说明不在当前页
          //可能是上一页在被缓存状态
          setHeight(res?.height || 0)
        }
      })
    }, 100),
    [],
  )

  useEffect(watchResize)

  return (
    <>
      <View
        catchMove
        className={classNames(className, 'fixe_view', `position_${position}`, uuid)}
        style={{
          ...style,
          '--fixed_view_z-index': highComp ? ZIndex.HighComp : zIndex,
        }}
        {...restProps}
      >
        {children}
      </View>
      {placeholder ? <View style={{ height }} className='fixed_view_placeholder'></View> : null}
    </>
  )
}
FixedView.defaultProps = {
  position: 'top',
  placeholder: false,
  highComp: false,
  zIndex: ZIndex.LowComp,
}
export default observer(FixedView)

import { View } from "@tarojs/components";
import React from "react";
import { useSafeAreaBottom } from "@/hook";

interface SafeAreaBottomProps {
  /**
   * 自定义样式
   */
  style?: React.CSSProperties;
  /**
   * 自定义类名
   */
  className?: string;
  /**
   * 背景色，默认透明
   */
  backgroundColor?: string;
}

/**
 * 底部安全区域组件
 * 用于在页面底部添加安全区域高度，避免内容被 iPhone 底部指示器遮挡
 */
const SafeAreaBottom: React.FC<SafeAreaBottomProps> = ({ 
  style, 
  className, 
  backgroundColor = 'transparent' 
}) => {
  const { safeAreaBottom } = useSafeAreaBottom();

  // 如果没有底部安全区域，不渲染
  if (safeAreaBottom === 0) {
    return null;
  }

  return (
    <View
      className={className}
      style={{
        width:"100%",
        height: safeAreaBottom,
        backgroundColor,
        ...style,
      }}
    />
  );
};

export default SafeAreaBottom;

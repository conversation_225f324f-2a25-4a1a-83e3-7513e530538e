import { View, ScrollView, Image } from "@tarojs/components";
import { OrderItem, OrderGoods } from "@/mobx/model/Order";
import IconFont, { IconNames } from "@/components/iconfont";
import order from "@/http/order";

const OrderCard: React.FC<OrderItem> = (props) => {
  return (
    <View className={`orderCardCom w-130 border-1-area py-8 px-6 ${props.class}`} style={props.style}>
      <View className="flex-between mb-2 leading-8 text-sm ">
        <View className="font-medium text-brand-2D2E2C mb-1">Orders: #{props.order_sn}</View>
        <View className="text-brand-898989 flex-center font-medium">
          {props.order_status} <IconFont name="detail"></IconFont>
        </View>
      </View>
      <View className="flex-between mb-8  leading-8 text-sm">
        <View className=" text-brand-898989">{props.order_time}</View>
        <View className=" text-brand-C33333">{props.order_status}</View>
      </View>

      {/* 产品图片列表 */}
      {/* <ScrollView scrollX enhanced showScrollbar={false} type="list">
        <View className="flex-between">
          {props.goods.map((good) => (
            <Image key={good.goods_sn} src={good.goods_img} className="w-70 h-94 mr-4" mode="widthFix" />
          ))}
        </View>
      </ScrollView> */}
      {/* UI修改 */}
      <View className="relative">
        <View className="overflow-x-scroll box-border mt-5 ">
          <View className="flex ">
            {props.goods?.map((good, index) => (
              <View className="w-38 h-51 min-w-38 min-h-51 mr-2" key={index}>
                <Image className="w-70 h-94 mr-4" mode="aspectFill" key={good.goods_sn} src={good.goods_img} />
              </View>
            ))}
          </View>
        </View>
        <View
          className={["absolute w-43  h-94 top-0 right-0  box-border  pl-11 flex justify-center items-center text-brand-C33333 text-sm"].join(" ")}
          style={
            props.goods?.length >= 4
              ? {
                  background: "linear-gradient(269.26deg, #FAFAFA 61.35%, rgba(255, 255, 255, 0) 100.23%)",
                }
              : {}
          }>
          <View className="text-right mt-auto ml-auto">
            <View className="text-brand-898989">Qty.2</View>
            <View className="text-sm text-brand-2D2E2C mt-2">¥6360</View>
          </View>
        </View>
      </View>
      {props.order_status == "Confirm Delivery" && (
        <View className="flex-end items-end text-sm leading-8 mt-5">
          <View className="btn-black-auto py-3 px-8">Confirm Delivery</View>
        </View>
      )}
    </View>
  );
};
export default OrderCard;

import { View, Textarea } from "@tarojs/components";
import { useState, useEffect } from "react";

interface TextareaFieldProps {
  value?: string;
  onChange?: (value: string) => void;
  placeholder?: string;
  maxLength?: number;
  label?: string;
  className?: string;
  style?: React.CSSProperties;
}

const TextareaField: React.FC<TextareaFieldProps> = ({ value = "", onChange, placeholder = "请输入内容...", maxLength = 100, label, className = "", style = {} }) => {
  const [isFocused, setIsFocused] = useState(false);
  const [internalValue, setInternalValue] = useState(value);

  // 同步外部value
  useEffect(() => {
    setInternalValue(value);
  }, [value]);

  const handleFocus = () => {
    console.log("TextareaField: Focus");
    setIsFocused(true);
  };

  const handleBlur = () => {
    console.log("TextareaField: Blur");
    setIsFocused(false);
  };

  const handleInput = (e: any) => {
    const newValue = e.detail.value;
    setInternalValue(newValue);
    onChange?.(newValue);
  };

  const handleFakeTextareaClick = () => {
    setIsFocused(true);
  };

  return (
    <View className={`relative w-full ${className}`} style={style}>
      {/* 标签 */}
      {label && <View className="text-sm leading-8 text-gray-800 mb-2">{label}</View>}

      {/* 真实的Textarea - 只在聚焦时显示 */}
      {isFocused && (
        <Textarea
          value={internalValue}
          placeholder={placeholder}
          placeholderClass="input-placeholder"
          maxlength={maxLength}
          onInput={handleInput}
          onFocus={handleFocus}
          onBlur={handleBlur}
          cursor={internalValue.length}
          autoFocus
          className="input-textarea w-auto"
          style={{
            minHeight: "88px",
            padding: "7px 15px",
          }}
        />
      )}

      {/* 假的Textarea - 在失焦时显示 */}
      {!isFocused && (
        <View
          onClick={handleFakeTextareaClick}
          className={`input-textarea cursor-pointer flex items-start ${internalValue ? "text-black" : "text-brand-898989"}`}
          style={{
            minHeight: "88px",
            padding: "8px 16px",
          }}>
          {internalValue ? <View className="input-placeholder text-black">{internalValue}</View> : <View className="input-placeholder"> {placeholder}</View>}
        </View>
      )}
    </View>
  );
};

export default TextareaField;

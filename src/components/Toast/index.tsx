import { View, Text } from '@tarojs/components'
import { observer } from 'mobx-react'
import React from 'react'
import classNames from 'classnames'
import { useStores } from '@/hook'
import IconFont from '../iconfont'
import './index.scss'

export interface ToastProps {
  className?: string
}

const Toast: React.FC<ToastProps> = ({ className }) => {
  const { toastStore } = useStores()

  if (!toastStore.showToast) {
    return null
  }

  const handleViewClick = () => {
    if (toastStore.onViewClick) {
      toastStore.onViewClick()
    }
  }

  const handleToastClick = () => {
    toastStore.hide()
  }

  const getIconName = () => {
    switch (toastStore.toastIcon) {
      case 'success':
        return 'check_circle'
      case 'error':
        return 'error'
      case 'warning':
        return 'Vector'
      case 'info':
        return 'Vector'
      default:
        return 'check_circle'
    }
  }

  return (
    <View className={classNames('custom-toast', className)} onClick={handleToastClick}>
      <View className='custom-toast__content'>
        <View className='custom-toast__main'>
          <View className='custom-toast__icon'>
            <IconFont name={getIconName()} size={20} color='#fff' />
          </View>
          <Text className='custom-toast__text'>{toastStore.toastContent}</Text>
        </View>
        {toastStore.showViewButton && (
          <View className='custom-toast__action' onClick={handleViewClick}>
            <Text className='custom-toast__action-text'>View</Text>
            <IconFont name='tiaozhuan' size={14} color='#fff' />
          </View>
        )}
      </View>
    </View>
  )
}

export default observer(Toast)

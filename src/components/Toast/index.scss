.custom-toast {
  position: fixed;
  bottom: 124px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 9999;
  max-width: 90%;
  // min-width: 280px;
  animation: slideDown 0.3s ease-out;

  &__content {
    background: rgba(0, 0, 0, 0.8);
    border-radius: 8px;
    padding: 12px 16px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    backdrop-filter: blur(10px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }

  &__main {
    display: flex;
    align-items: center;
    flex: 1;
  }

  &__icon {
    margin-right: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  &__text {
    color: #fff;
    font-size: 14px;
    line-height: 20px;
    flex: 1;
  }

  &__action {
    display: flex;
    align-items: center;
    margin-left: 16px;
    padding: 4px 8px;
    border-radius: 4px;
    background: rgba(255, 255, 255, 0.1);
    cursor: pointer;
    transition: background 0.2s ease;

    &:active {
      background: rgba(255, 255, 255, 0.2);
    }
  }

  &__action-text {
    color: #fff;
    font-size: 12px;
    margin-right: 4px;
    font-weight: 500;
  }
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
}

// 退出动画
.custom-toast.exit {
  animation: slideUp 0.3s ease-in forwards;
}

@keyframes slideUp {
  from {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
  to {
    opacity: 0;
    transform: translateX(-50%) translateY(-20px);
  }
}

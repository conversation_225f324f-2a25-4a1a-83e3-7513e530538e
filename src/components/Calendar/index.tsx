import React, { useEffect, useState } from "react";
import { Text, View, Button } from "@tarojs/components";
import { SafeAreaBottom } from "@/components";
import { Popup } from "@nutui/nutui-react-taro";
import Taro from "@tarojs/taro";

import "./index.scss";
import IconFont from "../iconfont";

interface CalendarProps {
  visible: boolean;
  onClose: () => void;
  returnDate: (dateRange: { start_date: string; end_date: string }) => void;
  title?: string;
}

const Calendar: React.FC<CalendarProps> = ({ visible, onClose, returnDate, title = "Select Date" }) => {
  const weekArr = ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"];
  const MonthArr = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"];
  // 12点之前 预约时间节点为一天后开始，过12点，预约时间节点为两天后开始
  const timeH = new Date().getHours() > 12 ? 2 : 1;

  const [currentMonth, setMonth] = useState(new Date().getMonth() + 1);
  const [currentYear, setYear] = useState(new Date().getFullYear());
  const [byChooseDay, setByChooseDay] = useState<number>(1);
  const [currentMonthArr, setMonthArr] = useState<number[]>([]);
  const [oldDate, setOldDate] = useState(false);
  const [dayNow, setDayNow] = useState(new Date().getDate());
  const [chooseDay, setChooseDay] = useState<number>(new Date().getDate());
  const [chooseMonth, setChooseMonth] = useState<number>(new Date().getMonth() + 1);
  const [chooseYear, setChooseYear] = useState<number>(new Date().getFullYear());
  //是否是第七个月当月
  const [isSeven, setSeven] = useState(false);

  // 区间选择相关状态
  const [startDate, setStartDate] = useState<{ year: number; month: number; day: number } | null>(null);
  const [endDate, setEndDate] = useState<{ year: number; month: number; day: number } | null>(null);

  // 格式化日期为 YYYY-MM-DD
  const formatDateToString = (year: number, month: number, day: number): string => {
    return `${year}-${String(month).padStart(2, "0")}-${String(day).padStart(2, "0")}`;
  };

  // 检查日期是否是今天之后（未来的日期）
  const isFutureDate = (year: number, month: number, day: number): boolean => {
    const today = new Date();
    const checkDate = new Date(year, month - 1, day);
    const todayDate = new Date(today.getFullYear(), today.getMonth(), today.getDate());
    return checkDate > todayDate;
  };

  // 检查日期是否在选择范围内
  const isInRange = (year: number, month: number, day: number): boolean => {
    if (!startDate || !endDate) return false;

    const checkDate = new Date(year, month - 1, day);
    const start = new Date(startDate.year, startDate.month - 1, startDate.day);
    const end = new Date(endDate.year, endDate.month - 1, endDate.day);

    return checkDate >= start && checkDate <= end;
  };

  // 检查是否是选择的开始或结束日期
  const isSelectedDate = (year: number, month: number, day: number): boolean => {
    if (startDate && startDate.year === year && startDate.month === month && startDate.day === day) {
      return true;
    }
    if (endDate && endDate.year === year && endDate.month === month && endDate.day === day) {
      return true;
    }
    return false;
  };
  const oldDay = (year, month) => {
    const yearNow = new Date().getFullYear();
    const monthNow = new Date().getMonth() + 1;

    if (year < yearNow) {
      setOldDate(true);
    } else if (year === yearNow && month < monthNow) {
      setOldDate(true);
    } else if (year === yearNow && month > monthNow + 5) {
      setOldDate(true);
    } else if (year > yearNow && month + (12 - monthNow) > 6) {
      setOldDate(true);
    } else {
      setOldDate(false);
    }
    if ((year = yearNow + 1 && month + (12 - monthNow) === 6)) {
      setSeven(true);
    } else {
      setSeven(false);
    }
  };
  // 获取某年某月总共多少天
  const getDateLen = (year, month) => {
    const actualMonth = month - 1;
    const timeDistance = +new Date(year, month) - +new Date(year, actualMonth);
    return timeDistance / (1000 * 60 * 60 * 24);
  };
  // 获取某月1号是周几
  const getFirstDateWeek = (year, month) => {
    return new Date(year, month - 1, 1).getDay();
  };
  // 获取当月数据，返回数组
  const getCurrentArr = (year, month) => {
    const currentMonthDateLen = getDateLen(year, month); // 获取当月天数
    const currentMonthDateArr: number[] = []; // 定义空数组
    if (currentMonthDateLen > 0) {
      for (let i = 1; i <= currentMonthDateLen; i++) {
        currentMonthDateArr.push(i);
      }
    }
    return currentMonthDateArr;
  };
  // 上月多余数据
  const getPreArr = (year, month) => {
    let preMonthDateLen = getFirstDateWeek(year, month);
    const date: number[] = [];
    if (preMonthDateLen === 0) {
      preMonthDateLen = 7;
    }
    for (let i = 0; i < preMonthDateLen; i++) {
      date.push(0);
    }
    return date;
  };
  // 整合当月所有数据
  const getAllArr = (year, month) => {
    oldDay(year, month);
    const currentArr = getCurrentArr(year, month);
    const preArr = getPreArr(year, month);
    let allArr = [...preArr, ...currentArr];
    const counts = {};
    for (let i = 0; i < allArr.length; i++) {
      const item = allArr[i];
      counts[item] = counts[item] ? counts[item] + 1 : 1;
    }
    if (counts[0] >= 7) {
      allArr = allArr.slice(counts[0], allArr.length);
    }

    setMonthArr(allArr);
  };
  // 上月 年、月
  const preMonth = (year, month) => {
    if (month == 1) {
      return {
        year: --year,
        month: 12,
      };
    } else {
      return {
        year: year,
        month: --month,
      };
    }
  };
  // 下月 年、月
  const nextMonth = (year, month) => {
    if (month == 12) {
      return {
        year: ++year,
        month: 1,
      };
    } else {
      return {
        year: year,
        month: ++month,
      };
    }
  };
  // 点击 上月
  const gotoPreMonth = () => {
    const { year, month } = preMonth(currentYear, currentMonth);
    // if (month < new Date().getMonth() + 1 && year <= new Date().getFullYear()) {
    // return;
    // } else {
    setMonth(month);
    setYear(year);
    getAllArr(year, month);
    // }
  };
  // 点击 下月
  const gotoNextMonth = () => {
    const { year, month } = nextMonth(currentYear, currentMonth);
    const addMonth = year > new Date().getFullYear() ? month : 0;
    if (year >= new Date().getFullYear() && 11 - new Date().getMonth() + addMonth > 6) {
      return;
    } else {
      setMonth(month);
      setYear(year);
      getAllArr(year, month);
    }
  };

  //选择日期区间 - 支持选择开始和结束日期
  const selectDay = (item) => {
    // 跳过空白日期
    if (item === 0) return;

    // 检查是否是未来的日期（今天之后不可选）
    if (isFutureDate(currentYear, currentMonth, item)) {
      return;
    }

    const selectedDate = { year: currentYear, month: currentMonth, day: item };

    if (!startDate) {
      // 第一次选择，设置开始日期
      setStartDate(selectedDate);
      setEndDate(null);
      console.log("Selected start date:", formatDateToString(currentYear, currentMonth, item));
    } else if (!endDate) {
      // 第二次选择，设置结束日期
      const startDateTime = new Date(startDate.year, startDate.month - 1, startDate.day);
      const selectedDateTime = new Date(currentYear, currentMonth - 1, item);

      if (selectedDateTime >= startDateTime) {
        // 选择的日期在开始日期之后，设置为结束日期
        setEndDate(selectedDate);
        console.log("Selected end date:", formatDateToString(currentYear, currentMonth, item));
      } else {
        // 选择的日期在开始日期之前，重新设置开始日期
        setStartDate(selectedDate);
        setEndDate(null);
        console.log("Reset start date:", formatDateToString(currentYear, currentMonth, item));
      }
    } else {
      // 已经选择了完整区间，重新开始选择
      setStartDate(selectedDate);
      setEndDate(null);
      console.log("Reset selection, new start date:", formatDateToString(currentYear, currentMonth, item));
    }
  };

  // 确认选择
  const handleConfirm = () => {
    if (!startDate || !endDate) {
      console.log("Please select both start and end dates");
      return;
    }

    const start_date = formatDateToString(startDate.year, startDate.month, startDate.day);
    const end_date = formatDateToString(endDate.year, endDate.month, endDate.day);

    console.log("Confirmed date range:", { start_date, end_date });
    returnDate({ start_date, end_date });
    onClose();
  };

  // 重置选择
  const handleReset = () => {
    // 重置为默认选择（昨天和今天）
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(today.getDate() - 1);

    const yesterdayObj = {
      year: yesterday.getFullYear(),
      month: yesterday.getMonth() + 1,
      day: yesterday.getDate(),
    };

    const todayObj = {
      year: today.getFullYear(),
      month: today.getMonth() + 1,
      day: today.getDate(),
    };

    setStartDate(yesterdayObj);
    setEndDate(todayObj);
    console.log("Reset to default selection");
  };

  useEffect(() => {
    getAllArr(currentYear, currentMonth);

    // 默认选择昨天和今天（因为今天之后的日期不可选）
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(today.getDate() - 1);

    const yesterdayObj = {
      year: yesterday.getFullYear(),
      month: yesterday.getMonth() + 1,
      day: yesterday.getDate(),
    };

    const todayObj = {
      year: today.getFullYear(),
      month: today.getMonth() + 1,
      day: today.getDate(),
    };

    setStartDate(yesterdayObj);
    setEndDate(todayObj);

    console.log("Default selection:", {
      start_date: formatDateToString(yesterdayObj.year, yesterdayObj.month, yesterdayObj.day),
      end_date: formatDateToString(todayObj.year, todayObj.month, todayObj.day),
    });
  }, []);

  // 半年后的切换箭头置灰
  const unSelect = currentYear >= new Date().getFullYear() && 11 - new Date().getMonth() + (currentYear > new Date().getFullYear() ? currentMonth : 0) > 5;

  return (
    <Popup visible={visible} position="bottom" onClose={onClose} round closeable closeIcon={<IconFont name="close" size={16}></IconFont>} closeIconPosition="top-right" style={{ height: "auto" }}>
      <View className="p-6">
        {/* 标题 */}
        <View className="text-left mb-6">
          <Text className="text-lg font-semibold" style={{ color: "#2D2E2C" }}>
            {title}
          </Text>
        </View>

        {/* 显示当前选择的区间 */}
        {startDate && endDate && (
          <View className="mb-4 py-3 px-6 rounded-sm  leading-12" style={{ backgroundColor: "#F6F6F6" }}>
            <Text className="text-sm text-center" style={{ color: "#2D2E2C" }}>
              {formatDateToString(startDate.year, startDate.month, startDate.day)} to {formatDateToString(endDate.year, endDate.month, endDate.day)}
            </Text>
          </View>
        )}

        {startDate && !endDate && (
          <View className="mb-4 py-3 px-6 rounded-sm leading-12" style={{ backgroundColor: "#F6F6F6" }}>
            <Text className="text-sm text-center" style={{ color: "#2D2E2C" }}>
              Start: {formatDateToString(startDate.year, startDate.month, startDate.day)} (Select end date)
            </Text>
          </View>
        )}

        {/* 原有的Calendar组件内容 */}
        <View className="Calendar pb-20">
          <View className="selectMonth">
            <View className="inline-block" onClick={gotoPreMonth}>
              <IconFont name="back" size={20} style={{ color: "#2D2E2C" }}></IconFont>
            </View>

            <View className="text-md mx-10">{MonthArr[currentMonth - 1]}</View>
            <View className="inline-block" onClick={gotoNextMonth}>
              <IconFont name="detail" size={20} style={{ color: "#2D2E2C" }}></IconFont>
            </View>
          </View>
          <View className="flex-around week">
            {weekArr.map((item, index) => {
              return (
                <View key={index} className="text-sm leading-8 text-brand-000000 min-w-8 text-center">
                  {item}
                </View>
              );
            })}
          </View>
          <View className="flex flex-wrap">
            {currentMonthArr.map((item, index) => {
              const isFuture = item !== 0 && isFutureDate(currentYear, currentMonth, item);
              const isSelected = item !== 0 && isSelectedDate(currentYear, currentMonth, item);
              const inRange = item !== 0 && isInRange(currentYear, currentMonth, item);

              return (
                <View
                  key={index}
                  className={`dayItem ${isFuture ? "colorf5" : ""}
                    ${isSelected ? "choosed" : ""}
                    ${inRange && !isSelected ? "in-range" : ""}`}
                  style={{
                    backgroundColor: isSelected ? "#2D2E2C" : inRange ? "#F6F6F6" : "transparent",
                    color: isSelected ? "#FFFFFF" : isFuture ? "#CCCCCC" : "#2D2E2C",
                    cursor: isFuture ? "not-allowed" : "pointer",
                  }}
                  onClick={() => {
                    selectDay(item);
                  }}>
                  {item === 0 ? " " : item}
                </View>
              );
            })}
          </View>
        </View>

        {/* 按钮区域 */}

        <Button className="btn-black-auto" onClick={handleConfirm}>
          Confirm
        </Button>

        <SafeAreaBottom></SafeAreaBottom>
      </View>
    </Popup>
  );
};
export default Calendar;

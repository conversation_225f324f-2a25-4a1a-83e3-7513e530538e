import { View } from "@tarojs/components";
import classnames from "classnames";
import React from "react";
import Taro from "@tarojs/taro";
import FixedView from "../FixedView";
import IconFont, { IconNames } from "@/components/iconfont";
import { scanCodeFn } from "@/utils";
import SafeAreaBottom from "../SafeAreaBottom";

// Tabbar 高度常量
export const TABBAR_HEIGHT = 60;

export interface TabbarProps {
  showTabbar?: boolean;
  className?: string;
}

interface TabItem {
  icon: IconNames;
  page: string;
  iconSize: number;
  title: string;
}

const Tabbar: React.FC<TabbarProps> = (props) => {
  const { showTabbar = false, className } = props;

  // 如果不显示 tabbar，返回 null
  if (!showTabbar) {
    return null;
  }

  // 定义 tab 项
  const tabItems: TabItem[] = [
    {
      icon: "home", // 使用 home 图标
      page: "/pages/home/<USER>",
      iconSize: 28,
      title: "Home Menu",
    },
    {
      icon: "shoppingbag",
      page: "/subpackages/mall/shopping-bag/index",
      iconSize: 28,
      title: "Shopping Bag",
    },
  ];

  // 处理 tab 点击
  const handleTabClick = (page: string) => {
    if (page === "/pages/home/<USER>") {
      Taro.switchTab({ url: page });
    } else {
      Taro.navigateTo({ url: page });
    }
  };

  // 处理扫码点击
  const handleScanClick = () => {
    scanCodeFn();
  };

  return (
    <FixedView
      className={classnames(
        "fixed bottom-0 left-0 right-0 bg-white border-t border-brand-E5E5E5 z-50",
        className
      )}
    >
      <View
        className="flex-around align-bottom px-10 box-border"
        style={{ height: `${TABBAR_HEIGHT}px` }}
      >
        {/* 左侧 tab - Home */}
        <View className="text-center">
          <View
            className="flex-center w-14 h-14 mx-auto"
            onClick={() => handleTabClick(tabItems[0].page)}
          >
            <IconFont
              name={tabItems[0].icon}
              size={tabItems[0].iconSize}
              color="#2D2E2C"
            />
          </View>
          <View className="text-xs text-brand-8A8A8A">{tabItems[0].title}</View>
        </View>
        {/* 中间扫码按钮 */}
        <View className="flex-center " onClick={handleScanClick}>
          <View className="w-24 h-24 bg-black rounded-full flex-center ">
            <IconFont name="scan" size={36} color="#fff" />
          </View>
        </View>

        {/* 右侧 tab - Shopping Bag */}
        <View className="text-center">
          <View
            className="flex-center  w-14 h-14 mx-auto"
            onClick={() => handleTabClick(tabItems[1].page)}
          >
            <IconFont
              name={tabItems[1].icon}
              size={tabItems[1].iconSize}
              color="#2D2E2C"
            />
          </View>
          <View className="text-xs text-brand-8A8A8A">{tabItems[1].title}</View>
        </View>
      </View>
      <SafeAreaBottom />
    </FixedView>
  );
};

export default Tabbar;

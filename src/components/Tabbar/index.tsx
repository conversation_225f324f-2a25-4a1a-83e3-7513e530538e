import { View } from "@tarojs/components";
import classnames from "classnames";
import React from "react";
import Taro from "@tarojs/taro";
import FixedView from "../FixedView";
import IconFont, { IconNames } from "@/components/iconfont";
import { scanCodeFn } from "@/utils";
import SafeAreaBottom from "../SafeAreaBottom";

export interface TabbarProps {
  showTabbar?: boolean;
  className?: string;
}

interface TabItem {
  icon: IconNames;
  page: string;
  iconSize: number;
  title: string;
}

const Tabbar: React.FC<TabbarProps> = (props) => {
  const { showTabbar = false, className } = props;

  // 如果不显示 tabbar，返回 null
  if (!showTabbar) {
    return null;
  }

  // 定义 tab 项
  const tabItems: TabItem[] = [
    {
      icon: "home", // 使用 store 图标代替 home
      page: "/pages/home/<USER>",
      title:"",
            iconSize: 28,
    },
    {
      icon: "shoppingbag",
      page: "/subpackages/mall/shopping-bag/index",
      iconSize: 28,
    },
  ];

  // 处理 tab 点击
  const handleTabClick = (page: string) => {
    if (page === "/pages/home/<USER>") {
      Taro.switchTab({ url: page });
    } else {
      Taro.navigateTo({ url: page });
    }
  };

  // 处理扫码点击
  const handleScanClick = () => {
    scanCodeFn();
  };

  return (
    <FixedView
      className={classnames(
        "fixed bottom-0 left-0 right-0 bg-white border-t border-brand-E5E5E5 z-50",
        className
      )}
      style={{ height: "80px" }}
    >
      <View className="flex items-end justify-between h-full px-10 box-border">
        {/* 左侧 tab - Home */}
        <View
          className="flex items-center justify-center w-14 h-14"
          onClick={() => handleTabClick(tabItems[0].page)}
        >
          <IconFont
            name={tabItems[0].icon}
            size={tabItems[0].iconSize}
            color="#2D2E2C"
          />
        </View>

        {/* 中间扫码按钮 */}
        <View
          className="flex items-center justify-center"
          onClick={handleScanClick}
        >
          <View className="w-12 h-12 bg-black rounded-full flex items-center justify-center">
            <IconFont name="scan" size={24} color="#fff" />
          </View>
        </View>

        {/* 右侧 tab - Shopping Bag */}
        <View
          className="flex items-center justify-center w-14 h-14"
          onClick={() => handleTabClick(tabItems[1].page)}
        >
          <IconFont
            name={tabItems[1].icon}
            size={tabItems[1].iconSize}
            color="#2D2E2C"
          />
        </View>
      </View>
      <SafeAreaBottom/>
    </FixedView>
  );
};

export default Tabbar;

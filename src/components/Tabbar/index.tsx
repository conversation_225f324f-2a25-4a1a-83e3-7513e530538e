import { View } from "@tarojs/components";
import classnames from "classnames";
import React from "react";
import Taro from "@tarojs/taro";
import "./index.scss";
import FixedView from "../FixedView";
import IconFont, { IconNames } from "@/components/iconfont";
import { scanCodeFn } from "@/utils";
import SafeAreaBottom from "../SafeAreaBottom";

export interface TabbarProps {
  showTabbar?: boolean;
  className?: string;
}

interface TabItem {
  icon: IconNames;
  page: string;
  iconSize: number;
}

const Tabbar: React.FC<TabbarProps> = (props) => {
  const { showTabbar = false, className } = props;

  // 如果不显示 tabbar，返回 null
  if (!showTabbar) {
    return null;
  }

  // 定义 tab 项
  const tabItems: TabItem[] = [
    {
      icon: "home", // 使用 store 图标代替 home
      page: "/pages/home/<USER>",
      iconSize: 28,
    },
    {
      icon: "shoppingbag",
      page: "/subpackages/mall/shopping-bag/index",
      iconSize: 28,
    },
  ];

  // 处理 tab 点击
  const handleTabClick = (page: string) => {
    if (page === "/pages/home/<USER>") {
      Taro.switchTab({ url: page });
    } else {
      Taro.navigateTo({ url: page });
    }
  };

  // 处理扫码点击
  const handleScanClick = () => {
    scanCodeFn();
  };

  return (
    <FixedView
      className={classnames("tabbar", className)}
      style={{
        bottom: 0,
        left: 0,
        right: 0,
        height: "80px",
        backgroundColor: "#fff",
        borderTop: "1px solid #E5E5E5",
        zIndex: 1000,
      }}
    >
      <View className="tabbar-content">
        {/* 左侧 tab - Home */}
        <View
          className="tabbar-item"
          onClick={() => handleTabClick(tabItems[0].page)}
        >
          <IconFont
            name={tabItems[0].icon}
            size={tabItems[0].iconSize}
            color="#2D2E2C"
          />
        </View>

        {/* 中间扫码按钮 */}
        <View className="tabbar-scan" onClick={handleScanClick}>
          <View className="scan-circle">
            <IconFont name="scan" size={24} color="#fff" />
          </View>
        </View>

        {/* 右侧 tab - Shopping Bag */}
        <View
          className="tabbar-item"
          onClick={() => handleTabClick(tabItems[1].page)}
        >
          <IconFont
            name={tabItems[1].icon}
            size={tabItems[1].iconSize}
            color="#2D2E2C"
          />
        </View>
      </View>
      <SafeAreaBottom></SafeAreaBottom>
    </FixedView>
  );
};

export default Tabbar;

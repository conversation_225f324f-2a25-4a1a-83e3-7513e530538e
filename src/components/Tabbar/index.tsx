import { View } from "@tarojs/components";
import classnames from "classnames";
import React, { useEffect, useState } from "react";
import Taro from "@tarojs/taro";
import FixedView from "../FixedView";
import IconFont, { IconNames } from "@/components/iconfont";
import { scanCodeFn, toJump, vibrateLight } from "@/utils";
import SafeAreaBottom from "../SafeAreaBottom";
import { userStore } from "@/mobx";

// Tabbar 高度常量
export const TABBAR_HEIGHT = 50;

export interface TabbarProps {
  showTabbar?: boolean;
  className?: string;
}

interface TabItem {
  icon: IconNames;
  page: string;
  iconSize: number;
  title: string;
}

const Tabbar: React.FC<TabbarProps> = (props) => {
  const { showTabbar = false, className } = props;
  const [currentRoute, setCurrentRoute] = useState("");

  // 如果不显示 tabbar，返回 null
  if (!showTabbar) {
    return null;
  }

  // 获取当前路由
  useEffect(() => {
    const getCurrentRoute = () => {
      const pages = Taro.getCurrentPages();
      if (pages.length > 0) {
        const currentPage = pages[pages.length - 1];
        const route = `/${currentPage.route}`;
        setCurrentRoute(route);
      }
    };

    getCurrentRoute();

    // 监听页面显示事件来更新路由状态
    const handlePageShow = () => {
      setTimeout(getCurrentRoute, 100); // 延迟一点确保路由更新
    };

    // 使用 Taro 的页面生命周期事件
    Taro.eventCenter.on("__taroPageShow", handlePageShow);

    return () => {
      Taro.eventCenter.off("__taroPageShow", handlePageShow);
    };
  }, []);

  // 判断是否为当前路由
  const isActiveRoute = (page: string) => {
    return currentRoute === page;
  };

  // 获取图标颜色
  const getIconColor = (page: string) => {
    return isActiveRoute(page) ? "#2D2E2C" : "#898989";
  };

  // 定义 tab 项
  const tabItems: TabItem[] = [
    {
      icon: "home", // 使用 home 图标
      page: "/pages/home/<USER>",
      iconSize: 28,
      title: "Home Menu",
    },
    {
      icon: "shoppingbag",
      page: "/pages/shopping-bag/index",
      iconSize: 28,
      title: "Shopping Bag",
    },
  ];

  // 处理 tab 点击
  const handleTabClick = (page: string) => {
    // 触发震动反馈
    vibrateLight('light');

    if (page.includes("shopping")) {
      if (!userStore.userInfo.username) {
        toJump("/pages/login/index");
        return;
      }
    }

    // 使用 switchTab 跳转 tabBar 页面
    if (page === "/pages/home/<USER>" || page === "/pages/shopping-bag/index") {
      Taro.switchTab({ url: page });
    } else {
      toJump(page);
    }
  };

  // 处理扫码点击
  const handleScanClick = () => {
    // 触发震动反馈
    vibrateLight('light');
    scanCodeFn();
  };

  return (
    <FixedView
      className={classnames(
        "fixed bottom-0 left-0 right-0 bg-white border-t border-brand-E5E5E5 z-50 border-t-1",
        className
      )}
    >
      <View
        className="flex-around align-bottom px-10 box-border"
        style={{ height: `${TABBAR_HEIGHT}px` }}
      >
        {/* 左侧 tab - Home */}
        <View className="text-center mt-auto">
          <View
            className="flex-center w-14 h-14 mx-auto"
            onClick={() => handleTabClick(tabItems[0].page)}
          >
            <IconFont
              name={tabItems[0].icon}
              size={tabItems[0].iconSize}
              color={getIconColor(tabItems[0].page)}
            />
          </View>
          <View
            className="text-xs"
            style={{ color: getIconColor(tabItems[0].page) }}
          >
            {tabItems[0].title}
          </View>
        </View>
        {/* 中间扫码按钮 */}
        <View className="flex-center  mt-2" onClick={handleScanClick}>
          <View className="w-24 h-24 bg-black rounded-full flex-center ">
            <IconFont name="scan" size={36} color="#fff" />
          </View>
        </View>

        {/* 右侧 tab - Shopping Bag */}
        <View className="text-center  mt-auto">
          <View
            className="flex-center  w-14 h-14 mx-auto"
            onClick={() => handleTabClick(tabItems[1].page)}
          >
            <IconFont
              name={tabItems[1].icon}
              size={tabItems[1].iconSize}
              color={getIconColor(tabItems[1].page)}
            />
          </View>
          <View
            className="text-xs"
            style={{ color: getIconColor(tabItems[1].page) }}
          >
            {tabItems[1].title}
          </View>
        </View>
      </View>
      <SafeAreaBottom />
    </FixedView>
  );
};

export default Tabbar;

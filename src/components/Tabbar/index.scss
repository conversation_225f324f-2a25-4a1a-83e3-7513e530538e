.tabbar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background-color: #fff;
  border-top: 1px solid #E5E5E5;
  height: 80px;
  box-sizing: border-box;

  .tabbar-content {
    display: flex;
    align-items: flex-end;
    justify-content: space-between;
    height: 100%;
    padding: 0 40px;
    box-sizing: border-box;
  }

  .tabbar-item {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 60px;
    height: 60px;
    cursor: pointer;
    transition: opacity 0.2s;

    &:hover {
      opacity: 0.7;
    }
  }

  .tabbar-scan {
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: transform 0.2s;

    &:hover {
      transform: scale(1.05);
    }

    .scan-circle {
      width: 48px;
      height: 48px;
      background-color: #000;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}
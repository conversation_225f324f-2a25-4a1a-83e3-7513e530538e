.loading {
  position: fixed;
  z-index: var(--loading-z-index, 9970);
  left: 0;
  top: 0;
  bottom: 0;
  right: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(2px);
  transition: opacity 0.3s ease;

  // 全屏模式样式
  &--fullscreen {
    z-index: var(--loading-fullscreen-z-index, 9999);
    background-color: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(4px);
  }

  // 隐藏状态
  &.height_hidden {
    opacity: 0;
    pointer-events: none;
  }

  .loading_content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    // padding: 32px;
  }

  .loading_img {
    // font-size: 80px;
    // color: #1890ff;
    // margin-bottom: 16px;

    // 旋转动画
    .nut-icon-am-rotate {
      animation: loading-rotate 1s linear infinite;
    }

    // loading gif样式
    .loading-gif {
      width: 120px;
    }
  }

  .loading_text {
    font-size: 28px;
    color: #666;
    text-align: center;
    line-height: 1.4;
    margin-top: 8px;
  }
}

// 旋转动画关键帧
@keyframes loading-rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

// 隐藏类，用于优化性能
.height_hidden {
  opacity: 0 !important;
  pointer-events: none !important;
}

import { View, Text, Image } from '@tarojs/components'
import { getCurrentInstance } from '@tarojs/runtime'
import classNames from 'classnames'
import { observer } from 'mobx-react'
import { useEffect, useRef, useState } from 'react'

import { useStores } from '@/hook'
import './index.scss'
// import { Refresh } from '@nutui/icons-react-taro'
import loadingGif from '@/assets/loading.gif'

// Loading组件的Props接口
export interface LoadingProps {
  // 自定义加载文本
  text?: string
  // 自定义样式类名
  className?: string
  // 自定义样式
  style?: React.CSSProperties
  // 是否显示加载文本
  showText?: boolean
  // 自定义加载图标
  icon?: React.ReactNode
}

const Loading: React.FC<LoadingProps> = (props) => {
  const {
    text = '加载中...',
    className,
    style,
    showText = false,
    icon
  } = props

  const { loadingStore } = useStores()
  const registerPagePath = useRef<any>('')
  const [isShow, setIsShow] = useState<boolean>(false)

  useEffect(() => {
    const pageInstance = getCurrentInstance()
    const currentPath = pageInstance?.router?.$taroPath
    registerPagePath.current = currentPath
  }, [])

  useEffect(() => {
    const pageInstance = getCurrentInstance()
    const currentPath = pageInstance?.router?.$taroPath
    setIsShow(currentPath === registerPagePath.current && loadingStore.isShow)
  }, [loadingStore.isShow])

  // 根据loadingStore的isFullScreen状态决定z-index
  const zIndexStyle = loadingStore.isFullScreen
    ? { zIndex: 'var(--loading-fullscreen-z-index, 9999)' }
    : { zIndex: 'var(--loading-z-index, 9970)' }

  //hide的时候不要去除元素 免得重绘页面导致image要重新计算高度导致闪烁
  return (
    <View
      className={classNames(
        'loading',
        {
          'loading--fullscreen': loadingStore.isFullScreen,
          'height_hidden': !isShow
        },
        className
      )}
      catchMove
      style={{
        ...zIndexStyle,
        ...style
      }}
    >
      <View className='loading_content'>
        <View className='loading_img'>
          {icon || <Image src={loadingGif} mode='widthFix' className='loading-gif' />}
        </View>
        {showText && (
          <Text className='loading_text'>{text}</Text>
        )}
      </View>
    </View>
  )
}

export default observer(Loading)

import { View, Text } from "@tarojs/components";
import IconFont, { IconNames } from "@/components/iconfont";

interface ErrorMessageProps {
  msg: string;
  icon?: IconNames;
  className?: string;
  noIcon?: boolean;
}

const ErrorMessage: React.FC<ErrorMessageProps> = ({
  msg,
  icon = "error",
  className = "",
  noIcon = false,
}) => {
  if (!msg) return null;
  return (
    <View
      className={`flex flex-col items-center justify-center text-center h-auto text-brand-2D2E2C  pb-50 ${className}`}
    >
      {noIcon ? null : (
        <View className="flex justify-center items-center mb-2">
          <IconFont name={icon} size={32} />
        </View>
      )}

      <Text className="text-sm font-medium">{msg}</Text>
    </View>
  );
};

export default ErrorMessage;

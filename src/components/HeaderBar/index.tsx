import { systemInfo, getNavBarInfo } from "@/utils";
import { responsiveStyle } from "@/utils/responsive";

import { View, Image } from "@tarojs/components";
import classnames from "classnames";
import React from "react";
import { ArrowLeft, More } from "@nutui/icons-react-taro";
import Taro from "@tarojs/taro";
import "./index.scss";
import FixedView from "../FixedView";
import Icon from "@/assets/logo.svg";

// 头部样式类型
export type HeaderBarType =
  | "basic" // 基础样式：只有标题
  | "withBack" // 带返回按钮
  | "withBackIcon" // 带返回按钮和左侧图标
  | "withBackMenu" // 带返回按钮和右侧菜单
  | "withMenu" // 只有右侧菜单，无返回按钮
  | "custom"; // 完全自定义

// 右侧操作按钮配置
export interface RightAction {
  icon?: React.ReactNode;
  text?: string;
  onClick?: () => void;
}

export interface HeaderBarProps {
  // 基础配置
  headerTitle?: string;
  backgroundColor?: string;
  color?: string;

  // 样式类型
  type?: HeaderBarType;

  // 左侧配置
  showBack?: boolean;
  leftIcon?: React.ReactNode;
  onBackClick?: () => void;

  // 中间配置
  centerIcon?: React.ReactNode;

  // 右侧配置
  rightActions?: RightAction[];
  showMenu?: boolean;
  onMenuClick?: () => void;

  // 自定义内容
  leftContent?: React.ReactNode;
  centerContent?: React.ReactNode;
  rightContent?: React.ReactNode;

  // 其他
  className?: string;
}

const HeaderBar: React.FC<HeaderBarProps> = (props) => {
  const {
    type = "withBack",
    headerTitle = "SUITSUPPLY",
    backgroundColor,
    color,
    showBack,
    leftIcon,
    onBackClick,
    centerIcon,
    rightActions = [],
    showMenu = false,
    onMenuClick,
    leftContent,
    centerContent,
    rightContent,
    className,
  } = props;

  const defaultGoBack = () => {
    if (Taro.getCurrentPages().length > 1) {
      if (Taro.getCurrentPages()[1]?.route != "page/login/index" && Taro.getCurrentPages()[1]?.route != "subpackages/cash/orderInfo/index") {
        Taro.navigateBack({ delta: 1 });
      } else {
        if (Taro.getCurrentPages()[0]?.route?.includes("subpackages/cash")) {
          Taro.navigateTo({
            url: "/subpackages/cash/index/index",
          });
        } else {
          Taro.switchTab({
            url: "/pages/home/<USER>",
          });
        }
      }
    } else {
      Taro.reLaunch({
        url: "/pages/home/<USER>",
      });
    }
  };

  const handleBackClick = () => {
    if (onBackClick) {
      onBackClick();
    } else {
      defaultGoBack();
    }
  };

  const navBarInfo = getNavBarInfo();
  const style = { height: navBarInfo.navBarHeight };

  // 响应式样式示例
  const responsiveStyles = responsiveStyle({
    iconSize: 20, // 图标大小 20px (375px设计稿)
    titleSize: 18, // 标题大小 18px
    padding: 10, // 内边距 10px
    actionPadding: 6, // 操作按钮内边距 6px
  });

  // 根据类型决定是否显示返回按钮
  const shouldShowBack = type === "custom" ? showBack : ["withBack", "withBackIcon", "withBackMenu"].includes(type);

  // 根据类型决定是否显示菜单
  const shouldShowMenu = type === "custom" ? showMenu : ["withBackMenu", "withMenu"].includes(type);

  // 渲染左侧内容
  const renderLeftContent = () => {
    if (type === "custom" && leftContent) {
      return leftContent;
    }

    return (
      <View
        className={classnames(
          {
            left_box: true,
            is_multi: shouldShowBack,
          },
          "flex-1"
        )}
        style={style}>
        {shouldShowBack && (
          <View className="left_icon" onClick={handleBackClick}>
            <ArrowLeft size="20px" />
          </View>
        )}
        {(type === "withBackIcon" || leftIcon) && leftIcon && <View className="left_icon">{leftIcon}</View>}
      </View>
    );
  };

  // 渲染中间内容
  const renderCenterContent = () => {
    if (type === "custom" && centerContent) {
      return centerContent;
    }

    return (
      <View className="middle_box flex-1" style={style}>
        <Image src={Icon} ></Image>
        {/* {centerIcon && <View className="center_icon">{centerIcon}</View>}
        {headerTitle && <View className="header_title">{headerTitle}</View>} */}
      </View>
    );
  };

  // 渲染右侧内容
  const renderRightContent = () => {
    if (type === "custom" && rightContent) {
      return rightContent;
    }

    return (
      <View className="right_box flex-1" style={style}>
        {rightActions.map((action, index) => (
          <View key={index} className="right_action" onClick={action.onClick}>
            {action.icon && action.icon}
            {action.text && <View className="action_text">{action.text}</View>}
          </View>
        ))}
        {shouldShowMenu && (
          <View className="right_action" onClick={onMenuClick}>
            <More size="20px" />
          </View>
        )}
      </View>
    );
  };

  return (
    <FixedView
      className={classnames("header_bar pb-5", className)}
      style={
        {
          "--headerbar-bg-color": backgroundColor || "",
          "--headerbar-color": color || "",
          paddingTop: systemInfo?.statusBarHeight,
        } as any
      }
      highComp>
      {renderLeftContent()}
      {renderCenterContent()}
      {renderRightContent()}
    </FixedView>
  );
};

export default HeaderBar;

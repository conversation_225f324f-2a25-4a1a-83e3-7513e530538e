.header_bar {
  width: 100%;
  min-height: 44px;
  color: var(--headerbar-color, #000);
  background-color: var(--headerbar-bg-color, #fff);
  box-sizing: border-box;
  display: flex;
  flex-direction: row;
  align-items: flex-end;
  position: fixed;
  left: 0;
  top: 0;
  padding-top: env(safe-area-inset-top);
  z-index: 1;

  // 左侧区域
  .left_box {
    display: flex;
    align-items: center;
    height: 44px;
    padding-left: 10px;
    flex-shrink: 0;

    .left_icon {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 6px 12px;
      cursor: pointer;
      position: relative;

      &:not(:first-child)::before {
        content: "";
        position: absolute;
        width: 1px;
        height: 18.5px;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        background-color: currentColor;
        opacity: 0.4;
      }
    }

    .iconfont {
      font-size: 20px;
    }
  }

  // 中间区域
  .middle_box {
    flex: 1;
    height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    font-weight: 500;
    padding: 0 10px;

    image {
      width: 113px;
      display: flex;
      align-items: center;
      margin-right: 8px;
			height: 100%;
    }

    .header_title {
      text-align: center;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }

  // 右侧区域
  .right_box {
    display: flex;
    align-items: center;
    height: 44px;
    padding-right: 10px;
    flex-shrink: 0;

    .right_action {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 6px 12px;
      cursor: pointer;
      margin-left: 4px;

      .action_text {
        font-size: 16px;
        margin-left: 4px;
      }
    }
  }
}

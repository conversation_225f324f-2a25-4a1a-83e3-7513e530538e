.main_layout {
	height: 100vh;
	position: relative;

	.main_layout_content {
		// 确保内容区域可以滚动
		overflow-y: auto;
		// 确保内容从容器顶部开始
		position: relative;
		box-sizing: border-box;
		// 重置可能的样式
		padding: 0;
		margin: 0;
		// 不在 CSS 中设置 margin-top，由 JavaScript 动态设置

		// 底部安全区域支持
		// 可以通过 CSS 变量访问底部安全区域高度
		// padding-bottom: var(--safe-area-bottom, 0);

		// 隐藏状态样式
		&--hidden {
			opacity: 0;
			pointer-events: none;
		}
	}
}

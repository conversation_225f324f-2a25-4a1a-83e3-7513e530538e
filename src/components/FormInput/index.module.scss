// FormInput 组件样式
.outerArea {
  margin-bottom: 20px;
  position: relative;

  :global {
    .nut-cell {
      padding: 0;
      .nut-input {
        border: 1px solid rgba(239, 239, 239, 1);
        border-radius: 4px;
        height: 44px;
        line-height: 44px;
        padding: 0 16px;
        font-weight: 300;
        font-size: 14px;
        line-height: 20px;

        .input-placeholder {
          color: #898989;
          font-weight: 400;
          font-size: 14px;
          line-height: 20px;
        }

        // 必填字段的placeholder样式 - 星号显示为红色
        .placeholder-required {
          color: #898989;
          font-weight: 400;
          font-size: 14px;
          line-height: 20px;
        }
      }

      .nut-form-item-body-tips {
        color: #c33333;
        font-weight: 400;
        font-size: 10px;
        line-height: 16px;
        text-align: center;
        margin-top: 4px;
      }
    }
  }

  // 外层容器样式
}

.outerRedArea {
  .nut-input {
    border-color: #c33333 !important;
  }
}

.pointerEventsNone {
  pointer-events: none;
}

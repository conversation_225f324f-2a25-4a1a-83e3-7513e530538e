import { View } from "@tarojs/components";
import { Form, Input } from "@nutui/nutui-react-taro";
import { useState, memo, useMemo, useEffect } from "react";
import sty from "./index.module.scss";

interface FormInputProps {
  name: string;
  // placeholder: string;
  label: string;
  type?: "text" | "number" | "password";
  required?: boolean;
  rules?: any;
  class_name?: string;
  style?: React.CSSProperties;
  showRequire?: boolean;
  value?: string; // 添加value prop用于判断是否有内容
}

const FormInput: React.FC<FormInputProps> = ({
  name,
  // placeholder,
  label,
  type = "text",
  required = false,
  rules = {},
  class_name = "",
  style = {},
  showRequire = false,
  value,
}) => {
  const [showAccountVerify, setShowAccountVerify] = useState(false);
  const [isAccountFocus, setIsAccountFocus] = useState(false);
  const [currentValue, setCurrentValue] = useState("");

  useEffect(() => {
    if (value !== undefined) {
      setCurrentValue(value);
    }
  }, [value]);

  // 合并状态计算，避免多次重新渲染
  const labelState = useMemo(() => {
    // 只有当值真正存在且不为空时才激活label
    const hasValue = currentValue || (value && value.trim());

    const isActive = isAccountFocus || showAccountVerify || hasValue;
    return {
      isActive,
      hasError: showAccountVerify,
      colorClass: showAccountVerify ? "text-brand-C33333" : "text-brand-898989",
    };
  }, [isAccountFocus, showAccountVerify, currentValue, value]);

  return (
    <View className={[sty.outerArea, labelState.hasError ? sty.outerRedArea : "", class_name].join(" ")} style={style}>
      <Form.Item
        align="center"
        required={required}
        name={name}
        rules={[
          rules?.required,
          rules?.validator && {
            validator: (ruleCfg: any, value: any) => rules.validator(ruleCfg, value, setShowAccountVerify),
          },
        ].filter(Boolean)}>
        <Input
          placeholder={""}
          type={type}
          onChange={(value) => setCurrentValue(value)}
          cursor={value?.length}
          onFocus={() => {
            setIsAccountFocus(true);
          }}
          onBlur={() => {
            setIsAccountFocus(false);
          }}
        />
      </Form.Item>

      <View
        className={`text-base absolute left-8 bg-white ${sty.pointerEventsNone} ${labelState.colorClass}`}
        style={{
          top: labelState.isActive ? "-8px" : "14px",
          transition: "all 0.1s ease-in",
          zIndex: 1,
        }}>
        <View className="flex items-center">
          {label}
          {showRequire && <span className="text-brand-C33333">*</span>}
        </View>
      </View>
    </View>
  );
};

export default memo(FormInput);

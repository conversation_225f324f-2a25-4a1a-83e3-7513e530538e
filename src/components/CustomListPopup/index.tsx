import { View } from "@tarojs/components";
import { Popup, Radio } from "@nutui/nutui-react-taro";
import { Checklist } from "@nutui/icons-react-taro";
import { useEffect, useState } from "react";
import IconFont from "../iconfont";

import <PERSON><PERSON>reaBottom from "../SafeAreaBottom";
import sty from "./index.modules.scss";



interface ListType {
  name: string;
  id: string | number;
}

interface CustomListPopupType {
  title:string
  defaultV: string|number
  list: ListType[];
  visible: boolean;
  setVisible:(value:boolean)=> void
  onConfirm:(value: string | number) => void
}

const CustomListPopup: React.FC<CustomListPopupType> = ({title,defaultV, setVisible,visible, list,onConfirm }) => {
  const [radioVal, setRadioVal] = useState<string|number>(1)
  useEffect(()=> {
    if(visible ) {
      console.log('-----list',list);
      console.log('-----defaultV',defaultV);
      
      setRadioVal(defaultV)
    }
  },[visible])


  return (
    <View>
      <Popup
        visible={visible}
        position="bottom"
        onClose={() => {
          setVisible(false)
        }}
        destroyOnClose={true}
      >
        <View className={[sty.customListPopup].join(" ")}>
          <View className="max-h-364 pt-9 px-8 box-border ">
            <View className="flex justify-between items-center">
              <View className="text-md text-brand-000000  ">
                {title}
              </View>
              <View className="size-12" onClick={() => setVisible(false)}>
                <IconFont size={24} name="close" color={'#000'}/>
              </View>
            </View>
            <Radio.Group
              defaultValue={defaultV}
              labelPosition="left"
              style={{ width: "100%" }}
              className="mt-2"
              onChange={(v)=> setRadioVal(v)}
            >
              {list?.map(item => {
                return (
                  <Radio
                    icon={<IconFont name="checkbox" size={20} />}
                    activeIcon={<IconFont name="checked" size={20} />}
                    value={item.id}
                  >
                    {item.name}
                  </Radio>
                );
              })}
            </Radio.Group>
          </View>


          {/* 操作按钮 */}
          <View className={["pt-6 px-8 mt-12", sty.bntArea].join(" ")}>
            <View className="btn-default text-base  flex justify-center items-center" onClick={()=>onConfirm(radioVal)}>
              Confirm
            </View>
          </View>


          <SafeAreaBottom />
        </View>
      </Popup>
    </View>
  );
};

export default CustomListPopup;

import Taro from '@tarojs/taro';
import en from './locales/en';
import zh_cn from './locales/zh';

// 支持的语言类型
export type Language = 'en' | 'zh_cn';

// 语言资源
const resources = {
  en,
  zh_cn,
};

// 默认语言
const DEFAULT_LANGUAGE: Language = 'en';

// 语言存储键
const LANGUAGE_STORAGE_KEY = 'app_language';

class I18n {
  private currentLanguage: Language = DEFAULT_LANGUAGE;
  private resources = resources;

  constructor() {
    this.init();
  }

  // 初始化
  private async init() {
    try {
      // 从本地存储获取语言设置
      const savedLanguage = await Taro.getStorage({ key: LANGUAGE_STORAGE_KEY });
      if (savedLanguage.data && this.isValidLanguage(savedLanguage.data)) {
        this.currentLanguage = savedLanguage.data;
      } else {
        // 如果没有保存的语言，尝试获取系统语言
        const systemInfo = await Taro.getSystemInfo();
        const systemLanguage = this.getSystemLanguage(systemInfo.language);
        this.currentLanguage = systemLanguage;
        await this.setLanguage(systemLanguage);
      }
			//!!!!!!不开放语言切换功能
    	this.currentLanguage = 'en';
    } catch (error) {
      console.warn('Failed to load language setting:', error);
      this.currentLanguage = DEFAULT_LANGUAGE;
    }
  }

  // 验证语言是否有效
  private isValidLanguage(lang: string): lang is Language {
    return ['en', 'zh_cn'].includes(lang);
  }

  // 获取系统语言
  private getSystemLanguage(systemLang: string): Language {
    if (systemLang.startsWith('zh')) {
      return 'zh_cn';
    }
    return 'en';
  }

  // 获取当前语言
  getCurrentLanguage(): Language {
    return this.currentLanguage;
  }

  // 设置语言
  async setLanguage(language: Language): Promise<void> {
    if (!this.isValidLanguage(language)) {
      console.warn(`Invalid language: ${language}`);
      return;
    }

    this.currentLanguage = language;
    
    try {
      await Taro.setStorage({
        key: LANGUAGE_STORAGE_KEY,
        data: language,
      });
    } catch (error) {
      console.error('Failed to save language setting:', error);
    }
  }

  // 获取翻译文本
  t(key: string, params?: Record<string, string | number>): string {
    const keys = key.split('.');
    let value: any = this.resources[this.currentLanguage];

    // 遍历键路径
    for (const k of keys) {
      if (value && typeof value === 'object' && k in value) {
        value = value[k];
      } else {
        // 如果当前语言没有找到，尝试使用默认语言
        if (this.currentLanguage !== DEFAULT_LANGUAGE) {
          let defaultValue: any = this.resources[DEFAULT_LANGUAGE];
          for (const dk of keys) {
            if (defaultValue && typeof defaultValue === 'object' && dk in defaultValue) {
              defaultValue = defaultValue[dk];
            } else {
              defaultValue = key; // 如果都没找到，返回原始键
              break;
            }
          }
          value = defaultValue;
        } else {
          value = key; // 返回原始键作为后备
        }
        break;
      }
    }

    // 如果最终值不是字符串，返回原始键
    if (typeof value !== 'string') {
      return key;
    }

    // 处理参数替换
    if (params) {
      return this.interpolate(value, params);
    }

    return value;
  }

  // 参数插值
  private interpolate(template: string, params: Record<string, string | number>): string {
    return template.replace(/\{(\w+)\}/g, (match, key) => {
      return params[key] !== undefined ? String(params[key]) : match;
    });
  }

  // 获取所有支持的语言
  getSupportedLanguages(): Array<{ code: Language; name: string }> {
    return [
      { code: 'en', name: 'English' },
      { code: 'zh_cn', name: '中文' },
    ];
  }
}

// 创建单例实例
const i18n = new I18n();

// 导出翻译函数
export const t = (key: string, params?: Record<string, string | number>): string => {
  return i18n.t(key, params);
};

// 导出其他方法
export const getCurrentLanguage = (): Language => i18n.getCurrentLanguage();
export const setLanguage = (language: Language): Promise<void> => i18n.setLanguage(language);
export const getSupportedLanguages = () => i18n.getSupportedLanguages();

export default i18n;

// Chinese language file
export default {
  // Common
  common: {
    confirm: "确认",
    cancel: "取消",
    save: "保存",
    delete: "删除",
    edit: "编辑",
    add: "添加",
    search: "搜索",
    loading: "加载中...",
    noData: "暂无数据",
    error: "错误",
    success: "成功",
    failed: "失败",
    retry: "重试",
    back: "返回",
    next: "下一步",
    previous: "上一步",
    submit: "提交",
    reset: "重置",
    close: "关闭",
    open: "打开",
    yes: "是",
    no: "否",
    ok: "确定",
    total: "总计",
    loadMore: "加载更多",
    noMoreData: "没有更多数据",
    pullToRefresh: "下拉刷新",
    releaseToRefresh: "释放刷新",
    refreshing: "刷新中...",
  },

  // Login
  login: {
    title: "登录",
    username: "用户名",
    password: "密码",
    loginButton: "登录",
    microsoftLogin: "微软账号登录",
    rememberMe: "记住我",
    forgotPassword: "忘记密码？",
    loginSuccess: "登录成功",
    loginFailed: "登录失败",
    invalidCredentials: "用户名或密码错误",
    pleaseEnterUsername: "请输入用户名",
    pleaseEnterPassword: "请输入密码",
  },

  // Home
  home: {
    title: "首页",
    webstore: "网店",
    customerProfile: "客户档案",
    shoppingBag: "购物袋",
    inventoryInquiry: "库存查询",
    customMade: "定制",
    orders: "订单",
    welcome: "欢迎",
    todaysSales: "今日销售额",
    todaysOrder: "今日订单",
    checkIn: "签到",
    scan: "扫码",
    shanghaiAnfuStore: "上海安福店",
  },

  // Customer
  customer: {
    title: "客户",
    guest: "访客",
    customersCheckedInToday: "今日签到客户",
    searchPlaceholder: "手机号/邮箱",
    searching: "搜索中...",
    noCustomersFound: "未找到客户",
    selectCustomer: "选择客户",
    customerInfo: "客户信息",
    phoneNumber: "手机号",
    firstName: "名",
    lastName: "姓",
    fullName: "姓名",
    email: "邮箱",
    noEmail: "无邮箱",
    noEmailShort: "-",
    searchFailed: "搜索失败",
    searchFailedRetry: "搜索失败，请重试",
    welcomeGuest: "欢迎访客",
    // Customer Detail Page
    switchCustomer: "切换客户",
    view: "查看",
    notes: "备注",
    editNote: "编辑备注",
    address: "地址",
    orderHistory: "订单历史",
    timeline: "时间线",
    sizePassport: "尺码护照",
    customization: "定制",
		registration:"注册",
  },

  // Registration
  registration: {
    title: "注册表单",
    signUp: "注册",
    phoneNumber: "手机号",
    firstName: "名",
    lastName: "姓",
    email: "邮箱",
    note: "备注",
    comments: "备注",
    registrationSuccessful: "注册成功！",
    registrationFailed: "注册失败！",
    pleaseFillRequiredFields: "请填写必填字段",
    pleaseEnterValidPhoneNumber: "请输入有效的手机号",
    pleaseEnterFirstName: "请输入您的名字",
    pleaseEnterLastName: "请输入您的姓氏",
    customerRegistrationSuccess: "客户注册成功",
    customerRegistrationFailed: "客户注册失败，请重试",
  },

  // Settings
  settings: {
    title: "设置",
    language: "系统语言",
    english: "英语",
    chinese: "中文",
    logout: "退出登录",
    paymentTerminal: "支付终端",
    switchSellingLocation: "切换销售地点",
    version: "版本",
    about: "关于",
    help: "帮助",
    feedback: "反馈",
    privacy: "隐私政策",
    terms: "服务条款",
  },

  // Validation
  validation: {
    required: "此字段为必填项",
    invalidEmail: "邮箱格式不正确",
    invalidPhone: "手机号格式不正确",
    minLength: "最少需要 {min} 个字符",
    maxLength: "最多允许 {max} 个字符",
    phoneRequired: "手机号为必填项",
    firstNameRequired: "名字为必填项",
    lastNameRequired: "姓氏为必填项",
    emailInvalid: "请输入有效的邮箱地址",
  },

  // Error Messages
  error: {
    networkError: "网络错误，请检查网络连接",
    serverError: "服务器错误，请稍后重试",
    unknownError: "发生未知错误",
    requestTimeout: "请求超时",
    unauthorized: "未授权访问",
    forbidden: "访问被禁止",
    notFound: "资源未找到",
  },
};

// English language file
export default {
  // Common
  common: {
    confirm: "Confirm",
    cancel: "Cancel",
    save: "Save",
    delete: "Delete",
    edit: "Edit",
    add: "Add",
    search: "Search",
    loading: "Loading...",
    noData: "No data",
    error: "Error",
    success: "Success",
    failed: "Failed",
    retry: "Retry",
    back: "Back",
    next: "Next",
    previous: "Previous",
    submit: "Submit",
    reset: "Reset",
    close: "Close",
    open: "Open",
    yes: "Yes",
    no: "No",
    ok: "OK",
    total: "Total",
    loadMore: "Load More",
    noMoreData: "No more data",
    pullToRefresh: "Pull to refresh",
    releaseToRefresh: "Release to refresh",
    refreshing: "Refreshing...",
  },

  // Login
  login: {
    title: "Login",
    username: "<PERSON>rna<PERSON>",
    password: "Password",
    loginButton: "Login",
    microsoftLogin: "Login with Microsoft",
    rememberMe: "Remember me",
    forgotPassword: "Forgot password?",
    loginSuccess: "Login successful",
    loginFailed: "Login failed",
    invalidCredentials: "Invalid username or password",
    pleaseEnterUsername: "Please enter account number",
    pleaseEnterPassword: "Please enter password",
  },

  // Home
  home: {
    title: "Home",
    webstore: "Webstore",
    customerProfile: "Customer Profile",
    shoppingBag: "Shopping Bag",
    inventoryInquiry: "Inventory Inquiry",
    customMade: "Custom Made",
    orders: "Order History",
    welcome: "Welcome",
    todaysSales: "Today's Sales",
    todaysOrder: "Today's Order",
    checkIn: "Check-in",
    scan: "Scan",
    shanghaiAnfuStore: "Shanghai Anfu Store",
  },

  // Customer
  customer: {
    title: "Customer",
    guest: "Guest",
    customersCheckedInToday: "Customers Checked-In Today",
    searchPlaceholder: "Phone Number/Email",
    searching: "Searching...",
    noCustomersFound: "No member information found.",
    selectCustomer: "Select Customer",
    customerInfo: "Customer Information",
    phoneNumber: "Phone Number",
    firstName: "First Name",
    lastName: "Last Name",
    fullName: "Full Name",
    email: "Email",
    noEmail: "No email",
    noEmailShort: "-",
    searchFailed: "Search failed",
    searchFailedRetry: "Search failed, please retry",
    welcomeGuest: "Welcome guest",
    // Customer Detail Page
    switchCustomer: "Switch Customer",
    view: "View",
    notes: "Notes",
    editNote: "Edit Note",
    address: "Address",
    orderHistory: "Order History",
    timeline: "Timeline",
    sizePassport: "Size Passport",
    customization: "Customization",
		registration:"Registration"
  },

  // Registration
  registration: {
    title: "Registration Form",
    signUp: "Sign-Up",
    phoneNumber: "Phone Number",
    firstName: "First Name",
    lastName: "Last Name",
    email: "Email",
    note: "Note",
    comments: "Comments",
    registrationSuccessful: "Registration successful!",
    registrationFailed: "Registration failed!",
    pleaseFillRequiredFields: "Please fill in required fields",
    pleaseEnterValidPhoneNumber: "Please type in valid phone number",
    pleaseEnterFirstName: "Please enter your first name",
    pleaseEnterLastName: "Please enter your last name",
    customerRegistrationSuccess: "Customer registration successful",
    customerRegistrationFailed: "Customer registration failed, please retry",
  },

  // Settings
  settings: {
    title: "Settings",
    language: "System Language",
    english: "English",
    chinese: "Chinese",
    logout: "Log Out",
    paymentTerminal: "Payment Terminal",
    switchSellingLocation: "Switch Selling Location",
    version: "Version",
    about: "About",
    help: "Help",
    feedback: "Feedback",
    privacy: "Privacy Policy",
    terms: "Terms of Service",
  },

  // Validation
  validation: {
    required: "This field is required",
    invalidEmail: "Invalid email format",
    invalidPhone: "Invalid phone number format",
    minLength: "Minimum length is {min} characters",
    maxLength: "Maximum length is {max} characters",
    phoneRequired: "Phone number is required",
    firstNameRequired: "First name is required",
    lastNameRequired: "Last name is required",
    emailInvalid: "Please enter a valid email address",
  },

  // Error Messages
  error: {
    networkError: "Network error, please check your connection",
    serverError: "Server error, please try again later",
    unknownError: "Unknown error occurred",
    requestTimeout: "Request timeout",
    unauthorized: "Unauthorized access",
    forbidden: "Access forbidden",
    notFound: "Resource not found",
  },
};

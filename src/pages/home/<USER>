import { View } from "@tarojs/components";
import { observer } from "mobx-react";
import { useEffect, useState } from "react";
import Taro from "@tarojs/taro";
import { Image, Divider } from "@nutui/nutui-react-taro";

import { MainLayout } from "@/components";
import { useI18n, useStores } from "@/hook";
import { toJump, scanCodeFn } from "@/utils";

import sty from "./index.module.scss";
import IconFont, { IconNames } from "@/components/iconfont";
import http from "@/http";
import NotLoginPage from "@/components/NotLoginPage";

const HomePage = () => {
  const { t } = useI18n();
  const { userStore, settingStore, customerStore } = useStores();
  const { userInfo, isLogin } = userStore;
  const { currentCustomer } = customerStore;
  const { shopList } = settingStore;
  const [shopName, setShopName] = useState();
  const [todaySale, setTodaySale] = useState<{
    total_amount?: number;
    total_order?: number;
  }>({});

  const [row_one] = useState([
    {
      iconNmae: "productList" as IconNames,
      text: t("home.webstore"),
      page: "subpackages/mall/catalog/index",
    },
    {
      iconNmae: "customDetail" as IconNames,
      text: t("home.customerProfile"),
      page: `${currentCustomer?.uid ? `subpackages/customer/detail/index?id=${currentCustomer.uid}&mobile=${currentCustomer.mobile}` : "subpackages/customer/index/index"}`,
      dot: isLogin && !!currentCustomer?.uid,
    },
    {
      iconNmae: "shoppingbag" as IconNames,
      text: t("home.shoppingBag"),
      page: "subpackages/mall/shopping-bag/index",
    },
  ]);

  useEffect(() => {
    console.log("------currentCustomer", currentCustomer);

    console.log("---row_one-", row_one);
  }, [currentCustomer]);

  const row_two = [
    // {
    //   iconNmae: "store" as IconNames,
    //   text: t("home.inventoryInquiry")
    // },
    {
      iconNmae: "customMade" as IconNames,
      text: t("home.customMade"),
      page: "subpackages/mall/custom-made/index",
    },
    {
      iconNmae: "orderList" as IconNames,
      text: t("home.orders"),
      page: "subpackages/mallorder-history/index",
    },
    {},
  ];
  useEffect(() => {
    if (!isLogin) return;
    settingStore.getShopList();
    getSales();
  }, [isLogin]);

  useEffect(() => {
    if (!isLogin) return;
    if (!userInfo?.zd_id || !shopList.length) return;
    const res: any = shopList.filter((item) => item.id == userInfo?.zd_id);
    setShopName(res[0].name);
  }, [shopList?.length, userInfo?.zd_id]);

  const getSales = async () => {
    const res: any = await http.common.getSale({});
    setTodaySale(res.data);
  };
  const turnCustomPage = () => {
    if (!currentCustomer?.uid) {
      toJump("/pages/customer/index/index");
    } else {
      toJump("subpackages/customer/detail/index");
    }
  };

  return (
    <MainLayout
      initOptions={{
        inited: false,
        initLoading: false,
        loadingFullScreen: true,
      }}
      // 使用便捷配置方式
      headerType="basic"
      headerBackgroundColor="#FAFAFA"
      style={{
        backgroundSize: "100% auto",
        backgroundColor: "#FAFAFA",
      }}
      showTabbar={true}
      showBottomPlaceholder={false}>
      <View className={sty.home_page}>
        <View className="mx-8 mt-4 p-6 border-1-area box-border bg-white">
          <View className="flex items-center">
            <View className="size-28">
              <Image radius="50%" src={userInfo.avatar} />
            </View>
            <View className="ml-4 flex-1">
              {isLogin ? (
                <>
                  <View className="flex justify-between h-12 items-center">
                    <View className="font-bold text-lg ">{userInfo?.name}</View>
                    <View onClick={() => toJump("/pages/setting/index")}>
                      <IconFont name="setting" size={20} color={"rgb(118,118,118)"} />
                    </View>
                  </View>
                  <View className="leading-10 text-base mt-2">{shopName}</View>
                </>
              ) : (
                <View className="text-lg font-medium text-brand-2D2E2C">Log In</View>
              )}
            </View>
          </View>
          <Divider
            style={
              {
                "--nutui-divider-text-color": "#EFEFEF",
                "--nutui-divider-margin": `${Taro.pxTransform(6)} 0 0`,
              } as React.CSSProperties
            }
          />
          <View className="h-42  py-10 flex justify-between items-center box-border">
            <View className="flex-1 flex flex-col justify-center items-center mt-2">
              <View className="font-medium leading-12 text-lg">{isLogin ? "¥" + todaySale.total_amount : "-"}</View>
              <View className="text-sm leading-8 text-brand-898989">{t("home.todaysSales")}</View>
            </View>
            <Divider
              style={
                {
                  "--nutui-divider-text-color": "#E1E1E1",
                  "--nutui-divider-vertical-height": `${Taro.pxTransform(16)}`,
                  "--nutui-divider-margin": `0 ${Taro.pxTransform(8)} `,
                } as React.CSSProperties
              }
              direction="vertical"
            />
            <View className="flex-1 flex flex-col justify-center items-center mt-2">
              <View className="font-medium leading-12 text-lg">{isLogin ? todaySale.total_order : "-"}</View>
              <View className="text-sm leading-8 text-brand-898989">{t("home.todaysOrder")}</View>
            </View>
          </View>
        </View>

        <View className="m-8  rounded-default overflow-hidden">
          <Image height={Taro.pxTransform(148)} src={userInfo.shop_img ?? "https://news.winshang.com/member/FCK/2019/7/8/201978173319952237x.jpg"} />
        </View>

        <View className="mx-8 py-10 px-6 bg-white border-1-area ">
          <View className="row flex h-26">
            {row_one.map((item) => {
              return (
                <View
                  className="flex  flex-1 flex-col justify-center items-center "
                  key={item.text}
                  onClick={() => {
                    item.text == "customer Profile" ? turnCustomPage() : toJump(item.page);
                  }}>
                  <View className="size-12 relative">
                    <IconFont name={item.iconNmae} size={24} />
                    <View className={sty.dot} style={{ display: item.dot ? "block" : "none" }}></View>
                  </View>
                  <View className="leading-8 text-sm font-light text-brand-2D2E2C mt-2">{item.text}</View>
                </View>
              );
            })}
          </View>

          <View className="row flex mt-10 h-26">
            {row_two.map((item) => {
              return (
                <View className="flex  flex-1 flex-col justify-center items-center" key={item.text}>
                  <View className="size-12">
                    <IconFont name={item.iconNmae} size={24} />
                  </View>
                  <View className="leading-8 text-sm font-light text-brand-2D2E2C mt-2">{item.text}</View>
                </View>
              );
            })}
          </View>
        </View>

        {!isLogin && <NotLoginPage />}
      </View>
    </MainLayout>
  );
};

export default observer(HomePage);

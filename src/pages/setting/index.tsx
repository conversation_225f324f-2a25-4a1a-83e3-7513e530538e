import { View } from "@tarojs/components";
import { MainLayout } from "@/components";
import CustomListPopup from "@/components/CustomListPopup";
import { useEffect, useState } from "react";
import { Dialog } from "@nutui/nutui-react-taro";
import IconFont from "@/components/iconfont";
import { useI18n, useStores } from "@/hook";
import http from "@/http";
import { observer } from "mobx-react";
import Taro from "@tarojs/taro";

const Setting = () => {
  const { t, currentLanguage, setLanguage, getSupportedLanguages } = useI18n();
  const [visible, setVisible] = useState(false);
  const [showLogoutPopup, setShowLogoutPopup] = useState(false);
  const { settingStore, userStore, toastStore } = useStores();
  const { paymentList, shopList } = settingStore;
  const { language, zd_id, payment, token } = userStore.userInfo;

  const [list, setList] = useState<any[]>([]);
  const [type, setType] = useState("");

  const [info, setInfo] = useState({
    language,
    zd_id,
    payment
  });

  // 获取语言选项
  const languageOptions = getSupportedLanguages().map(lang => ({
    name: lang.name,
    id: lang.code
  }));

  // 获取当前语言显示名称
  const getCurrentLanguageName = () => {
    const currentLang = getSupportedLanguages().find(
      lang => lang.code === currentLanguage
    );
    return currentLang?.name || "English";
  };

  useEffect(() => {
    settingStore.getPaymentList();
    settingStore.getShopList();
  }, []);

  const onClickRow = (option, v, type) => {
    setVisible(true);
    setList(option);
    setType(type);
  };

  // 切换选项
  const onConfirm = v => {
    if (type == "language") {
      setLanguage(v);
    }
    setInfo({
      ...info,
      [type]: v
    });
    setVisible(false);
    onUpdateDeviceInfo({ [type]: v });
  };

  // 获取对应店铺名或设备名
  const onFindName = (options, t) => {
    const res = options.filter(item => item.id == t);
    return res?.[0]?.name;
  };

  const onUpdateDeviceInfo = async detail => {
    try {
      const res: any = await http.common.onChangeDeviceInfo(detail);

      if (res.code == 0) {
        userStore.onChangeUserInfo({
          ...userStore.userInfo,
          ...detail
        });

        toastStore.show("更改成功");
      } else {
        toastStore.show({ icon: "error", content: res.msg });
      }
    } catch (error) {
      console.log("----error-", error);
    }
  };

  const onConfirmLogout = async () => {
    try {
      const res: any = await http.common.onLogOut({});
      if (res.code == 0) {
        toastStore.show("退出登录成功");
        userStore.logout()
      }else {
        toastStore.show({ icon: "error", content: res.msg });
        
      }
    } catch (error) {}
    setShowLogoutPopup(false);
  };

  return (
    <MainLayout
      initOptions={{
        inited: false,
        initLoading: false,
        loadingFullScreen: true
      }}
      // 使用便捷配置方式
      headerType="withBack"
      headerBackgroundColor="#FAFAFA"

      style={{
        backgroundSize: "100% auto",
        backgroundColor: "#FAFAFA",

      }}
    >
      <View className="bg-brand-FAFAFA">
        <View className="mx-12 mt-8 mb-12 border-1-area px-8 bg-white">
          <View className="flex justify-between items-center h-30 border-b-1">
            <View className="text-base text-brand-2D2E2C">
              {t("settings.language")}
            </View>
            <View
              className="flex items-center h-10"
              onClick={() => {
                onClickRow(languageOptions, language, "language");
              }}
            >
              <View className="text-sm text-brand-898989">
                {getCurrentLanguageName()}
              </View>
              <View className="size-8 flex justify-center items-center ">
                <IconFont name="detail" size={16} />
              </View>
            </View>
          </View>

          <View className="flex justify-between items-center h-30 border-b-1">
            <View className="text-base text-brand-2D2E2C">
              {t("settings.paymentTerminal")}
            </View>
            <View
              className="flex items-center h-10"
              onClick={() => {
                onClickRow(paymentList, payment, "payment");
              }}
            >
              <View className="text-sm text-brand-898989">
                {onFindName(paymentList, info["payment"])}
              </View>
              <View className="size-8 flex justify-center items-center ">
                <IconFont name="detail" size={16} />
              </View>
            </View>
          </View>

          <View className="flex justify-between items-center h-30">
            <View className="text-base text-brand-2D2E2C">
              {t("settings.switchSellingLocation")}
            </View>
            <View
              className="flex items-center h-10"
              onClick={() => {
                onClickRow(shopList, zd_id, "zd_id");
              }}
            >
              <View className="text-sm text-brand-898989 max-w-93">
                {onFindName(shopList, info["zd_id"])}
              </View>
              <View className="size-8 flex justify-center items-center ">
                <IconFont name="detail" size={16} />
              </View>
            </View>
          </View>
        </View>
        <View
          className="mx-12 mt-8 border-1-area h-22 text-base text-brand-2D2E2C flex justify-center items-center bg-white"
          onClick={() => setShowLogoutPopup(true)}
        >
          {t("settings.logout")}
        </View>
        <CustomListPopup
          title={t("settings.language")}
          defaultV={info[type]}
          visible={visible}
          setVisible={setVisible}
          list={list}
          onConfirm={onConfirm}
        />
        <Dialog
          closeIcon={<IconFont name="close" size={24} color={"#000"} />}
          closeIconPosition="top-right"
          title={t("settings.logout")}
          visible={showLogoutPopup}
          onConfirm={onConfirmLogout}
          onCancel={() => setShowLogoutPopup(false)}
        >
          Are you sure you want to log out?
        </Dialog>
      </View>
    </MainLayout>
  );
};

export default observer(Setting);

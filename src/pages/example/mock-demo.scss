.mock-demo {
  padding: 32px;
  background-color: #f5f5f5;
  min-height: 100vh;

  .header {
    text-align: center;
    margin-bottom: 40px;

    .title {
      font-size: 36px;
      font-weight: bold;
      color: #333;
    }
  }

  .section {
    background-color: white;
    border-radius: 16px;
    padding: 32px;
    margin-bottom: 32px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);

    .section-title {
      font-size: 32px;
      font-weight: bold;
      color: #333;
      margin-bottom: 24px;
      display: block;
    }

    .button-group {
      display: flex;
      flex-direction: column;
      gap: 16px;

      .button {
        width: 100%;
        height: 88px;
        font-size: 28px;
        border-radius: 12px;
      }
    }

    .result-box {
      background-color: #f8f9fa;
      border: 2px solid #e9ecef;
      border-radius: 12px;
      padding: 24px;
      max-height: 600px;
      overflow-y: auto;

      .result-text {
        font-family: 'Courier New', monospace;
        font-size: 24px;
        line-height: 1.5;
        color: #495057;
        white-space: pre-wrap;
        word-break: break-all;
      }
    }
  }

  .footer {
    text-align: center;
    padding: 24px;

    .tip {
      font-size: 24px;
      color: #6c757d;
      line-height: 1.5;
    }
  }
}

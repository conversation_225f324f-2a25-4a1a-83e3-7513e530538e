import React, { useState } from 'react';
import { View, Text, Button } from '@tarojs/components';
import Taro from '@tarojs/taro';
import http from '@/http';
import { MainLayout } from '@/components';

import './mock-demo.scss';

const MockDemo: React.FC = () => {
  const [loginResult, setLoginResult] = useState<any>(null);
  const [userInfo, setUserInfo] = useState<any>(null);
  const [loading, setLoading] = useState(false);

  // 测试登录接口
  const handleTestLogin = async () => {
    setLoading(true);
    try {
      const res = await http.login.login({
        username: 'admin',
        password: '123456'
      });
      setLoginResult(res);
      
      // 登录成功后保存 token
      if (res.code === 200 && res.data?.access_token) {
        Taro.setStorageSync('token', res.data.access_token);
      }
    } catch (error) {
      console.error('登录失败:', error);
      setLoginResult({ error: String(error) });
    } finally {
      setLoading(false);
    }
  };

  // 测试获取用户信息
  const handleGetUserInfo = async () => {
    setLoading(true);
    try {
      // 使用 common 接口发送请求
      const res = await http.common.default({
        c: 'dianyuan',
        m: 'detail'
      });
      setUserInfo(res);
    } catch (error) {
      console.error('获取用户信息失败:', error);
      setUserInfo({ error: String(error) });
    } finally {
      setLoading(false);
    }
  };

  // 清除结果
  const handleClear = () => {
    setLoginResult(null);
    setUserInfo(null);
    Taro.removeStorageSync('token');
  };

  return (
    <MainLayout
      headerTitle="Mock 接口测试"
      headerType="withBack"
    >
      <View className='mock-demo'>
        <View className='header'>
          <Text className='title'>Taro Mock 接口测试</Text>
        </View>

      <View className='section'>
        <Text className='section-title'>接口测试</Text>
        <View className='button-group'>
          <Button 
            type='primary' 
            onClick={handleTestLogin}
            loading={loading}
            className='button'
          >
            测试登录接口
          </Button>
          <Button 
            type='primary' 
            onClick={handleGetUserInfo}
            loading={loading}
            className='button'
          >
            测试获取用户信息
          </Button>
          <Button 
            type='default' 
            onClick={handleClear}
            className='button'
          >
            清除结果
          </Button>
        </View>
      </View>

      {loginResult && (
        <View className='section'>
          <Text className='section-title'>登录结果</Text>
          <View className='result-box'>
            <Text className='result-text'>{JSON.stringify(loginResult, null, 2)}</Text>
          </View>
        </View>
      )}

      {userInfo && (
        <View className='section'>
          <Text className='section-title'>用户信息</Text>
          <View className='result-box'>
            <Text className='result-text'>{JSON.stringify(userInfo, null, 2)}</Text>
          </View>
        </View>
      )}

        <View className='footer'>
          <Text className='tip'>提示: 确保在 config/index.js 中已启用 @tarojs/plugin-mock 插件</Text>
        </View>
      </View>
    </MainLayout>
  );
};

export default MockDemo;

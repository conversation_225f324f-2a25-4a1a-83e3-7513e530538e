import { View } from '@tarojs/components'
import { showLoading, showToast, hideLoading, navigateTo } from '@tarojs/taro'
import { scanCodeFn } from '@/utils'
import PrinterService from '@/utils/printer_blueTooth'
import WiFiPrinterService from '@/utils/printer_wifi'

import { observer } from 'mobx-react'

import { MainLayout } from '@/components'
import './index.scss'


// 定义首页组件
const indexPage = () => {
  // 实例化打印机服务
  const printer = new PrinterService()

  // 定义打印函数
  const printerFn = async () => {
    // 使用示例
    showLoading({ title: '准备打印...' })
    try {
      // 调用打印机服务打印
      await printer.print({
        no: '20231108001',
        time: '2023-11-08 14:30',
        items: [
          { name: '商品A', price: 25.0, qty: 2 },
          { name: '商品B', price: 15.5, qty: 3 },
        ],
        total: 25 * 2 + 15.5 * 3,
      })
      showToast({ title: '打印指令已发送', icon: 'success' })
    } catch (err) {
      showToast({ title: err.message, icon: 'none' })
    } finally {
      hideLoading()
    }
  }

  // 实例化wifi打印机服务
  const wifiPrinter = new WiFiPrinterService()
  // 定义wifi打印函数
  const printerWifiFn = async () => {
    // 使用示例
    showLoading({ title: '准备打印...' })
    try {
      // 使用示例
      wifiPrinter.printInvoice({
        no: '20231108001',
        time: '2023-11-08 14:30',
        items: [
          { name: '商品A', price: 25.0, qty: 2 },
          { name: '商品B', price: 15.5, qty: 3 },
        ],
        total: 25 * 2 + 15.5 * 3,
      })
      showToast({ title: '打印指令已发送', icon: 'success' })
    } catch (err) {
      showToast({ title: err.message, icon: 'none' })
    } finally {
      hideLoading()
    }
  }

  const series = [
    {
      data: [120, 200, 150, 80, 70, 110, 130],
      type: 'line',
      showBackground: true,
      backgroundStyle: {
        color: 'rgba(220, 220, 220, 0.8)',
      },
    },
  ]
  const xAxisConfing = {
    data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
  }

  const seriesPie = {
    data: [
      { value: 1048, name: 'Search Engine' },
      { value: 735, name: 'Direct' },
      { value: 580, name: 'Email' },
      { value: 484, name: 'Union Ads' },
      { value: 300, name: 'Video Ads' },
    ],
  }

  return (
    <MainLayout
      className='home_page'
      initOptions={{
        inited: true,
        initLoading: true,
      }}
    >
      <View className='index'>
        {/* Tailwind CSS 测试区域 */}
        <View className='p-4 mb-4'>
          <View className='text-2xl font-bold text-center mb-4 text-primary-600'>
            Tailwind CSS 测试
          </View>

          {/* 按钮测试 */}
          <View className='mb-6'>
            <View className='btn-primary text-center mb-3' onClick={scanCodeFn}>
              微信扫码 (Primary Button)
            </View>
            <View className='btn-secondary text-center mb-3' onClick={printerFn}>
              蓝牙连接 (Secondary Button)
            </View>
            <View className='bg-green-500 text-white px-4 py-2 rounded-lg text-center font-medium' onClick={printerWifiFn}>
              WiFi连接 (Custom Green)
            </View>
            <View className='bg-orange-500 text-white px-4 py-2 rounded-lg text-center font-medium mt-3' onClick={() => navigateTo({ url: '/pages/example/mock-demo' })}>
              Taro Mock 测试
            </View>
          </View>

          {/* 卡片测试 */}
          <View className='card mb-4'>
            <View className='text-lg font-semibold mb-2 text-gray-800'>
              功能卡片
            </View>
            <View className='text-gray-600 text-sm'>
              这是一个使用 Tailwind CSS 样式的卡片组件，展示了背景、圆角、阴影等效果。
            </View>
          </View>

          {/* 网格布局测试 */}
          <View className='grid grid-cols-2 gap-4 mb-4'>
            <View className='bg-blue-100 p-3 rounded-lg text-center'>
              <View className='text-blue-800 font-medium'>网格 1</View>
            </View>
            <View className='bg-purple-100 p-3 rounded-lg text-center'>
              <View className='text-purple-800 font-medium'>网格 2</View>
            </View>
          </View>

          {/* 颜色测试 */}
          <View className='flex flex-wrap gap-2 mb-4'>
            <View className='w-8 h-8 bg-red-500 rounded'></View>
            <View className='w-8 h-8 bg-green-500 rounded'></View>
            <View className='w-8 h-8 bg-blue-500 rounded'></View>
            <View className='w-8 h-8 bg-yellow-500 rounded'></View>
            <View className='w-8 h-8 bg-purple-500 rounded'></View>
          </View>
        </View>

        <View>
          {/* <LineEchart height={400} xAxis={xAxisConfing} series={series} /> */}
          {/* <PieEchart height={200} series={seriesPie} /> */}
        </View>
      </View>
    </MainLayout>
  )
}

export default observer(indexPage)

import { View, Image } from "@tarojs/components";
import { observer } from "mobx-react";
import { useStores, useI18n } from "@/hook";
import { MainLayout, FormInput } from "@/components";
import { Button as NutButton, Form } from "@nutui/nutui-react-taro";
import sty from "./index.module.scss";
import { toJump } from "@/utils";
import logo from "@/images/microsoft-logo.png";

console.log("-----sty", sty);

const LoginPage = () => {
  const { userStore } = useStores();
  const { t } = useI18n();

  return (
    <MainLayout
      initOptions={{
        inited: true,
        initLoading: true,
        loadingFullScreen: true,
      }}
      headerType="basic"
    >
      <View className={sty.content}>
        <View className={[sty.formArea, "ml-10 mr-10"].join(" ")}>
          <Form
            labelPosition="right"
            footer={
              <>
                <NutButton
                  nativeType="submit"
                  block
                  type="default"
                  className="btn-default mt-16"
                  formType="submit"
                >
                  Log In
                </NutButton>
              </>
            }
            validateTrigger="onBlur"
            onFinish={(values: any) => {
              console.log("表单提交数据:", values);
              userStore.loginBtnClick("account", {
                username: values.username,
                password: values.password,
              } as any);
            }}
            onFinishFailed={(values: any, errorFields: any) => {
              console.log("-----errorFields", errorFields);
              console.log("-----values", values);
            }}
          >
            <FormInput
              name="username"
              label="Account Number"
              type="text"
              required
              rules={{
                required: {
                  required: true,
                  message: t("login.pleaseEnterUsername"),
                },
                validator: (ruleCfg, value, setShowVerify) => {
                  if (!value) {
                    setShowVerify(true);
                    return false;
                  } else {
                    setShowVerify(false);
                    return true;
                  }
                },
              }}
            />

            <FormInput
              name="password"
              label="Enter Password"
              type="password"
              required
              rules={{
                required: {
                  required: true,
                  message: t("login.pleaseEnterPassword"),
                },
                validator: (ruleCfg, value, setShowVerify) => {
                  if (!value) {
                    setShowVerify(true);
                    return false;
                  } else {
                    setShowVerify(false);
                    return true;
                  }
                },
              }}
              style={{ marginBottom: 0 }}
            />
          </Form>
        </View>
      </View>
    </MainLayout>
  );
};

export default observer(LoginPage);

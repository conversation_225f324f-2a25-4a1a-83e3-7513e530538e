import Taro from "@tarojs/taro";
import { WebView } from "@tarojs/components";
import { useEffect, useState } from "react";
import { observer } from "mobx-react";
import { useStores } from "@/hook";
import { buildMicrosoftAuthUrl, validateMicrosoftAuthConfig } from "@/utils/microsoftAuth";

const MicrosoftAuthWebView = () => {
  const { userStore } = useStores();
  const [authUrl, setAuthUrl] = useState("https://pos-h5.mybaiqiu.com/callback/index.html");
  useEffect(() => {
    try {
      // 验证配置
      if (!validateMicrosoftAuthConfig()) {
        console.error('微软认证配置验证失败');
        Taro.showToast({
          title: "认证配置错误",
          icon: "none",
          duration: 3000,
        });
        return;
      }

      // 构建认证URL - 遵循正确的OAuth 2.0流程
      const { authUrl: builtAuthUrl, state, codeVerifier } = buildMicrosoftAuthUrl();
			console.log('authUrl',builtAuthUrl);
			
      // 存储状态和code verifier供后续验证使用
      Taro.setStorageSync('microsoft_auth_state', state);
      Taro.setStorageSync('microsoft_auth_code_verifier', codeVerifier);

      setAuthUrl(builtAuthUrl);

    } catch (error) {
      console.error('构建微软认证URL失败:', error);
      Taro.showToast({
        title: "认证初始化失败",
        icon: "none",
        duration: 3000,
      });
    }
  }, []);

  // 处理web-view的消息
  const handleMessage = (e: any) => {
    // console.log("收到web-view消息:", e.detail.data);

    const data = e.detail.data[0];
    if (data.type === "MICROSOFT_LOGIN_SUCCESS") {
      // 微软登录成功
      handleLoginSuccess(data.payload);
    } else if (data.type === "MICROSOFT_LOGIN_ERROR") {
      // 微软登录失败
			console.log(
				'收到微软登录失败消息: ', data.payload
			);
			
      handleLoginError(data.payload);
    }
  };

  // 处理登录成功
  const handleLoginSuccess = async (payload: any) => {
    try {
      await userStore.microsoftLoginSuccess({
        authorizationCode: payload.authorizationCode,
        codeVerifier: payload.codeVerifier,
        redirectUri: payload.redirectUri,
      });

      Taro.showToast({
        title: "登录成功",
        icon: "success",
        duration: 1500,
      });

      // 延迟跳转到首页
      setTimeout(() => {
        Taro.switchTab({ url: "/pages/home/<USER>" });
      }, 1500);
    } catch (error) {
      console.error("处理微软登录成功回调失败:", error);
      Taro.showToast({
        title: "登录处理失败",
        icon: "none",
        duration: 2000,
      });

      // 返回登录页
      Taro.navigateBack();
    }
  };

  // 处理登录失败
  const handleLoginError = (payload: any) => {
    console.error("微软登录失败:", payload);

    Taro.showToast({
      title: payload.message || "登录失败",
      icon: "none",
      duration: 2000,
    });

    // 返回登录页
    setTimeout(() => {
      Taro.navigateBack();
    }, 2000);
  };

  if (!authUrl) {
    return null;
  }

  return <WebView src={authUrl} onMessage={handleMessage} />;
};

export default observer(MicrosoftAuthWebView);

# 产品详情页面 - 手势切换系统

## 功能描述

这个页面实现了一个基于Taro手势系统的产品详情页面，支持在两个不同的视图之间进行手势切换：

1. **产品视图（Product View）**：显示产品图片和基本信息
2. **详情视图（Details View）**：显示产品的详细信息和规格参数

## 手势操作

### 从产品视图切换到详情视图
- **手势**：在底部文案区域（约200px高度）向上滑动
- **触发条件**：滑动距离超过50px
- **动画效果**：页面向上滑出，详情视图从下方进入
- **视觉提示**：底部文案区域有灰色指示条和抓取光标

### 从详情视图切换到产品视图
- **手势**：在详情页面向下滑动
- **触发条件**：滑动距离超过50px
- **动画效果**：页面向下滑出，产品视图从上方进入

### 手势区域限制
- **产品视图**：只有底部文案区域（约200px高度）支持向上滑动手势
- **其他区域**：不响应手势操作，保持正常的页面滚动行为
- **详情视图**：整个页面都支持向下滑动手势

## 技术实现

### 核心组件
- `ProductDetail`：主组件，管理视图状态和手势逻辑
- `ProductView`：产品视图组件
- `DetailsView`：详情视图组件

### 手势系统
- 使用Taro的触摸事件API（`onTouchStart`、`onTouchMove`、`onTouchEnd`）
- 实现区域限制：只在底部文案区域启用手势
- 防止页面滚动冲突
- 支持阈值检测，确保手势的准确性

### 动画效果
- 使用CSS transform实现平滑的页面切换动画
- 使用cubic-bezier缓动函数提供自然的动画效果

## 使用方法

1. 在首页点击"测试手势切换产品详情页"按钮
2. 进入产品详情页面
3. 在底部文案区域向上滑动查看详情
4. 在详情页面向下滑动返回产品视图

## 文件结构

```
src/pages/product-detail/
├── index.tsx          # 主组件文件
├── index.scss         # 样式文件
└── README.md          # 说明文档
```

## 样式特性

- 响应式设计，适配不同屏幕尺寸
- 现代化的UI设计，符合移动端交互习惯
- 流畅的动画效果和过渡
- 清晰的手势提示和视觉指示器

## 注意事项

- 手势系统仅在移动端有效
- 产品视图只有底部文案区域支持手势操作
- 动画过程中会暂时禁用手势操作以防止冲突 

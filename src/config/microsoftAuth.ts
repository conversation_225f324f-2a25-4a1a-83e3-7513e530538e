// 微软OAuth配置文件
// 请根据您的应用注册信息修改以下配置

export const MICROSOFT_AUTH_CONFIG = {
  // 应用程序(客户端) ID - 需要在Microsoft Entra管理中心获取
  clientId: "0f267a3e-22dc-4927-a56e-2887d780f0a9", //目前只有一个即品牌提供的

  // 注意：不应该使用 'common'，应该使用具体的租户ID
  tenantId: "fbe43f29-18b2-46ca-a741-bcc4672ba19c", // 临时使用 organizations，建议获取具体租户ID

  // 重定向URI - 小程序使用H5中转页面 只有线上ip
  redirectUri: "https://pos-h5.ibaiqiu.com/callback/index.html",

  // 权限范围
  scopes: ["openid", "profile", "email", "User.Read"],

};

// 开发环境配置
export const DEV_CONFIG = {
  ...MICROSOFT_AUTH_CONFIG,
  // clientId: "dev-client-id",
  redirectUri: "https://pos-h5.ibaiqiu.com/callback/index.html",
};

// 生产环境配置
export const PROD_CONFIG = {
  ...MICROSOFT_AUTH_CONFIG,
  // clientId: "prod-client-id",
  redirectUri: "https://pos-h5.ibaiqiu.com/callback/index.html",
};

// 根据环境获取配置
export function getMicrosoftAuthConfig() {
  const isDev = process.env.NODE_ENV === "development";
  return isDev ? DEV_CONFIG : PROD_CONFIG;
}

// 配置验证 (可选)
export function validateMicrosoftAuthConfig(config: any) {
  const requiredFields = ["clientId", "tenantId", "redirectUri"];

  for (const field of requiredFields) {
    if (!config[field] || config[field] === "YOUR_CLIENT_ID") {
      throw new Error(`微软OAuth配置缺少必需字段: ${field}`);
    }
  }

  // 验证重定向URI格式
  try {
    new URL(config.redirectUri);
  } catch (error) {
    throw new Error("重定向URI格式无效");
  }

  return true;
}

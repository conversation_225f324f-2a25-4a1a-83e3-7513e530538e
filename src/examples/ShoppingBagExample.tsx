import React from "react";
import { observer } from "mobx-react";
import { View, Text, Button } from "@tarojs/components";
import { shoppingBagStore } from "@/mobx";
import { OrderGoods } from "@/mobx/model/Order";

// 示例：如何使用 ShoppingBagStore
const ShoppingBagExample: React.FC = observer(() => {
  // 模拟商品数据（基于mock/order.js中的数据结构）
  const sampleProduct: Partial<OrderGoods> = {
    goods_sn: "C25120A07",
    goods_name: "Navy Blue Suit",
    goods_price: 2500,
    goods_img: "https://cdn.suitsupply.com/image/upload/w_120,h_144,c_lfill,g_center,b_rgb:efefef,f_auto//suitsupply/campaigns/ss25/quicklinks/polos/polos_t-shirts_short-sleeve-polos.jpg",
    discription: "Premium navy blue wool suit with modern fit",
    color: "Navy",
    serial_no: "SN001",
    size: "M",
    barcode: "1234567890001",
    status: "可售",
    detail: {
      MasterProductId: "H7326A",
      Material: "Egyptian Cotton",
    },
    detail_img: [],
    cm_url: "",
    alteration_url: "",
  };

  // 添加商品到购物车
  const handleAddToCart = async () => {
    const success = await shoppingBagStore.addToCart(sampleProduct, 1);
    console.log("添加结果:", success);
  };

  // 设置商品折扣
  const handleSetDiscount = () => {
    const firstItem = shoppingBagStore.cartItems[0];
    if (firstItem) {
      shoppingBagStore.setItemDiscount(firstItem.cartItemId, 0.8); // 8折
    }
  };

  // 更新商品数量
  const handleUpdateQuantity = async () => {
    const firstItem = shoppingBagStore.cartItems[0];
    if (firstItem) {
      await shoppingBagStore.updateQuantity(firstItem.cartItemId, firstItem.quantity + 1);
    }
  };

  // 移除商品
  const handleRemoveItem = () => {
    const firstItem = shoppingBagStore.cartItems[0];
    if (firstItem) {
      shoppingBagStore.removeItem(firstItem.cartItemId);
    }
  };

  return (
    <View style={{ padding: "20px" }}>
      <Text style={{ fontSize: "18px", fontWeight: "bold", marginBottom: "20px" }}>
        购物车示例
      </Text>

      {/* 购物车摘要 */}
      <View style={{ marginBottom: "20px", padding: "10px", backgroundColor: "#f5f5f5" }}>
        <Text>购物车商品数量: {shoppingBagStore.totalQuantity}</Text>
        <Text>商品总数: {shoppingBagStore.cartItems.length}</Text>
        <Text>选中商品数: {shoppingBagStore.selectedItems.length}</Text>
        <Text>是否为空: {shoppingBagStore.isEmpty ? "是" : "否"}</Text>
        <Text>是否正在计算: {shoppingBagStore.isCalculating ? "是" : "否"}</Text>
      </View>

      {/* 金额信息 */}
      <View style={{ marginBottom: "20px", padding: "10px", backgroundColor: "#e8f5e8" }}>
        <Text>总数量: {shoppingBagStore.lastCalculation.totalQuantity}</Text>
        <Text>总金额: ¥{shoppingBagStore.lastCalculation.totalAmount}</Text>
        <Text>折扣金额: ¥{shoppingBagStore.lastCalculation.discountAmount}</Text>
        <Text>最终金额: ¥{shoppingBagStore.lastCalculation.finalAmount}</Text>
      </View>

      {/* 操作按钮 */}
      <View style={{ marginBottom: "20px" }}>
        <Button onClick={handleAddToCart} style={{ marginBottom: "10px" }}>
          添加商品到购物车
        </Button>
        <Button onClick={handleSetDiscount} style={{ marginBottom: "10px" }}>
          设置8折优惠
        </Button>
        <Button onClick={handleUpdateQuantity} style={{ marginBottom: "10px" }}>
          增加数量
        </Button>
        <Button onClick={handleRemoveItem} style={{ marginBottom: "10px" }}>
          移除第一个商品
        </Button>
        <Button onClick={() => shoppingBagStore.clearCart()} style={{ marginBottom: "10px" }}>
          清空购物车
        </Button>
      </View>

      {/* 购物车商品列表 */}
      <View>
        <Text style={{ fontSize: "16px", fontWeight: "bold", marginBottom: "10px" }}>
          购物车商品列表:
        </Text>
        {shoppingBagStore.cartItems.map((item) => (
          <View
            key={item.cartItemId}
            style={{
              padding: "10px",
              marginBottom: "10px",
              backgroundColor: item.selected ? "#fff3cd" : "#f8f9fa",
              border: "1px solid #dee2e6",
            }}
          >
            <Text>商品名称: {item.goods_name}</Text>
            <Text>商品编号: {item.goods_sn}</Text>
            <Text>条码: {item.barcode}</Text>
            <Text>单价: ¥{item.goods_price}</Text>
            <Text>数量: {item.quantity}</Text>
            <Text>是否选中: {item.selected ? "是" : "否"}</Text>
            {item.discount && item.discount < 1 && (
              <Text>折扣: {Math.round(item.discount * 100)}%</Text>
            )}
            <Text>小计: ¥{item.subtotal}</Text>
            <Button
              onClick={() => shoppingBagStore.toggleItemSelection(item.cartItemId)}
              style={{ marginTop: "5px" }}
            >
              {item.selected ? "取消选中" : "选中"}
            </Button>
          </View>
        ))}
        {shoppingBagStore.isEmpty && (
          <Text style={{ color: "#6c757d", fontStyle: "italic" }}>购物车为空</Text>
        )}
      </View>
    </View>
  );
});

export default ShoppingBagExample;

@import "./nut-ui-theme";
@import "../assets/font/index.scss";

page {
  // - readme zindex管理
  --popup-z-index: 10001;
  --backdrop-z-index: 10000;
  --loading-z-index: 9970;
  --loading-fullscreen-z-index: 9999;
  --nutui-popup-border-radius: 0; // 虽然隐藏但是需要在dom里避免切换闪烁优化体验
  --nutui-dialog-close-width: 24px;
  --nutui-dialog-close-color: #000;
  --nutui-dialog-header-font-size: 24px;
  --nutui-font-size-3: 20px;
  --nutui-dialog-content-text-align: center;
  --nutui-dialog-content-margin: 40px auto;
  --nutui-gray-6: #000;
  --nutui-toast-inner-padding: 5px;
  --nutui-toast-text-font-size: 14px !important;
  font-family: "GTAmericaRegular";

  ::-webkit-scrollbar {
    display: none;
    width: 0;
    height: 0;
    color: transparent;
  }
  // 使用于内部有image的 不然displaynone内部的image高度会变成0
  .height_hidden {
    overflow: hidden !important;
    height: 0 !important;
    padding: 0 !important;
    margin: 0 !important;
    border: 0 !important;
    //否则无法阻止内部的fixed元素
    visibility: hidden !important;
  }

  .display_none {
    display: none !important;
  }

  .text_center {
    text-align: center;
  }
}

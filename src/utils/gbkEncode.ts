export function encodeGBK(str: string): Uint8Array {
  // 只处理常用汉字和常用符号，遇到不认识的字符用问号
  const gbkMap: { [key: string]: [number, number] } = {
    '中': [0xd6, 0xd0], '文': [0xce, 0xc4], '：': [0xa3, 0xba], '。': [0xa1, 0xa3], '，': [0xa3, 0xac],
    '号': [0xba, 0xc5], '时': [0xca, 0xb1], '间': [0xbc, 0xe4], '商': [0xc9, 0xcc], '品': [0xc6, 0xb7],
    '名': [0xc3, 0xfb], '称': [0xb3, 0xc6], '单': [0xb5, 0xa5], '价': [0xbc, 0xdb], '数': [0xca, 0xfd],
    '量': [0xc1, 0xbf], '合': [0xba, 0xcf], '计': [0xbc, 0xe1], '元': [0xd4, 0xaa], '￥': [0xa1, 0xe7],
    '挂': [0xb9, 0xd2], '成': [0xb3, 0xc9], '功': [0xb9, 0xa6], '退': [0xcd, 0xcb],
    '包': [0xb0, 0xfc], '卡': [0xbf, 0xa8], '会': [0xbb, 0xe1], '员': [0xd4, 0xb1], '未': [0xce, 0xb4],
    '找': [0xd5, 0xd2], '到': [0xb5, 0xbd], '打': [0xb4, 0xf2], '印': [0xd3, 0xa1], '机': [0xbb, 0xfa],
    '请': [0xc7, 0xeb], '检': [0xbc, 0xec], '查': [0xb2, 0xe9], '设': [0xc9, 0xe8], '备': [0xb1, 0xb8],
    '开': [0xbf, 0xaa], '启': [0xc6, 0xf4], '并': [0xb2, 0xa2], '于': [0xd3, 0xda], '可': [0xbf, 0xc9],
    '发': [0xb7, 0xa2], '现': [0xcf, 0xd6], '状': [0xd7, 0xb4], '态': [0xcc, 0xac], '失': [0xca, 0xa7],
    '败': [0xb0, 0xdc], '连': [0xc1, 0xac], '接': [0xbd, 0xd3], '已': [0xd2, 0xd1], '知': [0xd6, 0xaa],
    '写': [0xd0, 0xb4], '特': [0xcc, 0xd8], '征': [0xd5, 0xf7], '指': [0xd6, 0xb8], '令': [0xc1, 0xee],
    '送': [0xcb, 0xcd],
    // ...可补充常用汉字
  };
  const out: number[] = [];
  for (let i = 0; i < str.length; i++) {
    const code = str.charCodeAt(i);
    if (code < 0x80) {
      out.push(code);
    } else if (gbkMap[str[i]]) {
      out.push(gbkMap[str[i]][0], gbkMap[str[i]][1]);
    } else {
      // 不认识的字符用问号
      out.push(0x3f);
    }
  }
  return new Uint8Array(out);
} 
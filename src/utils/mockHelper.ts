import { mockConfig, isModuleMocked, getModuleBaseURL } from '@/config/mockConfig';

// 打印当前mock配置状态
export const printMockStatus = () => {
  console.log('🎭 Mock配置状态:');
  console.log('================');
  console.log(`全局Mock开关: ${mockConfig.enabled ? '✅ 启用' : '❌ 禁用'}`);
  console.log(`环境变量 API_ENV: ${process.env.API_ENV}`);
  console.log(`环境变量 NODE_ENV: ${process.env.NODE_ENV}`);
  console.log('');
  
  console.log('📋 各模块状态:');
  Object.entries(mockConfig.modules).forEach(([module, enabled]) => {
    const baseURL = getModuleBaseURL(module as any);
    const status = enabled ? '🎭 Mock' : '🌐 真实接口';
    console.log(`  ${module.padEnd(10)}: ${status} - ${baseURL}`);
  });
  console.log('');
};

// 检查指定接口是否使用mock
export const checkApiMockStatus = (module: keyof typeof mockConfig.modules, apiName: string) => {
  const isMocked = isModuleMocked(module);
  const baseURL = getModuleBaseURL(module);
  
  console.log(`🔍 接口检查: ${module}.${apiName}`);
  console.log(`   状态: ${isMocked ? '🎭 使用Mock数据' : '🌐 使用真实接口'}`);
  console.log(`   地址: ${baseURL}`);
  
  return {
    isMocked,
    baseURL,
    module,
    apiName
  };
};

// 在开发环境下自动打印mock状态
if (process.env.NODE_ENV === 'development') {
  // 延迟执行，确保所有模块都已加载
  setTimeout(() => {
    printMockStatus();
  }, 1000);
}

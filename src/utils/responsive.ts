import Taro from '@tarojs/taro'

// 设计稿宽度
const DESIGN_WIDTH = 375

// 获取系统信息
let systemInfo: Taro.getSystemInfoSync.Result | null = null

const getSystemInfo = () => {
  if (!systemInfo) {
    systemInfo = Taro.getSystemInfoSync()
  }
  return systemInfo
}

/**
 * 根据375px设计稿计算响应式尺寸
 * @param size 设计稿中的px值
 * @returns 适配后的rpx值
 */
export const rpx = (size: number): string => {
  const info = getSystemInfo()
  const screenWidth = info.screenWidth || 375
  
  // 计算缩放比例
  const scale = screenWidth / DESIGN_WIDTH
  
  // 转换为rpx（小程序中750rpx = 屏幕宽度）
  const rpxValue = (size * 2 * scale)
  
  return `${rpxValue}rpx`
}

/**
 * 根据375px设计稿计算响应式尺寸（返回数值）
 * @param size 设计稿中的px值
 * @returns 适配后的rpx数值
 */
export const rpxNum = (size: number): number => {
  const info = getSystemInfo()
  const screenWidth = info.screenWidth || 375
  
  // 计算缩放比例
  const scale = screenWidth / DESIGN_WIDTH
  
  // 转换为rpx
  return size * 2 * scale
}

/**
 * 根据375px设计稿计算响应式尺寸（返回px值）
 * @param size 设计稿中的px值
 * @returns 适配后的px值
 */
export const px = (size: number): string => {
  const info = getSystemInfo()
  const screenWidth = info.screenWidth || 375
  
  // 计算缩放比例
  const scale = screenWidth / DESIGN_WIDTH
  
  return `${size * scale}px`
}

/**
 * 根据375px设计稿计算响应式尺寸（返回px数值）
 * @param size 设计稿中的px值
 * @returns 适配后的px数值
 */
export const pxNum = (size: number): number => {
  const info = getSystemInfo()
  const screenWidth = info.screenWidth || 375
  
  // 计算缩放比例
  const scale = screenWidth / DESIGN_WIDTH
  
  return size * scale
}

/**
 * 获取屏幕尺寸信息
 */
export const getScreenInfo = () => {
  const info = getSystemInfo()
  const scale = info.screenWidth / DESIGN_WIDTH
  
  return {
    screenWidth: info.screenWidth,
    screenHeight: info.screenHeight,
    designWidth: DESIGN_WIDTH,
    scale,
    statusBarHeight: info.statusBarHeight,
    safeAreaInsets: info.safeAreaInsets || {
      top: info.statusBarHeight || 0,
      bottom: 0,
      left: 0,
      right: 0
    }
  }
}

/**
 * 响应式样式对象
 * @param styles 样式对象，值为375px设计稿中的px值
 * @returns 转换后的样式对象
 */
export const responsiveStyle = (styles: Record<string, number | string>) => {
  const result: Record<string, string> = {}
  
  Object.keys(styles).forEach(key => {
    const value = styles[key]
    if (typeof value === 'number') {
      result[key] = rpx(value)
    } else {
      result[key] = value
    }
  })
  
  return result
}

/**
 * 创建响应式类名
 * @param baseClass 基础类名
 * @param breakpoints 断点配置
 * @returns 响应式类名
 */
export const responsiveClass = (
  baseClass: string,
  breakpoints?: {
    sm?: string  // 小屏幕 < 480px
    md?: string  // 中等屏幕 480px - 768px
    lg?: string  // 大屏幕 > 768px
  }
) => {
  if (!breakpoints) return baseClass
  
  const info = getSystemInfo()
  const screenWidth = info.screenWidth
  
  if (screenWidth < 480 && breakpoints.sm) {
    return `${baseClass} ${breakpoints.sm}`
  } else if (screenWidth >= 480 && screenWidth <= 768 && breakpoints.md) {
    return `${baseClass} ${breakpoints.md}`
  } else if (screenWidth > 768 && breakpoints.lg) {
    return `${baseClass} ${breakpoints.lg}`
  }
  
  return baseClass
}

export default {
  rpx,
  rpxNum,
  px,
  pxNum,
  getScreenInfo,
  responsiveStyle,
  responsiveClass
}

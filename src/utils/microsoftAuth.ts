// 微软认证工具函数
// 遵循正确的 OAuth 2.0 + PKCE 流程

import { getMicrosoftAuthConfig } from "@/config/microsoftAuth";

/**
 * 生成随机字符串
 */
export function generateRandomString(length: number = 32): string {
  const charset = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-._~';
  let result = '';
  for (let i = 0; i < length; i++) {
    result += charset.charAt(Math.floor(Math.random() * charset.length));
  }
  return result;
}

/**
 * 生成状态参数 (防止CSRF攻击)
 */
export function generateState(): string {
  return generateRandomString(32);
}

/**
 * 生成 PKCE code verifier
 */
export function generateCodeVerifier(): string {
  return generateRandomString(128);
}

/**
 * 生成 PKCE code challenge (简化版本)
 * 注意：在生产环境中应该使用 SHA256 哈希
 */
export function generateCodeChallenge(codeVerifier: string): string {
  // 简化版本：直接使用 code verifier
  // 生产环境应该使用: base64url(sha256(codeVerifier))
  return codeVerifier;
}

/**
 * 构建微软认证URL
 * 遵循正确的调用顺序：租户 -> 客户端
 */
export function buildMicrosoftAuthUrl(): {
  authUrl: string;
  state: string;
  codeVerifier: string;
} {
  const config = getMicrosoftAuthConfig();
  const state = generateState();
  const codeVerifier = generateCodeVerifier();
  const codeChallenge = generateCodeChallenge(codeVerifier);

  // 验证配置
  if (!config.tenantId || !config.clientId) {
    throw new Error('微软认证配置不完整：缺少 tenantId 或 clientId');
  }

  // 构建认证URL - 按照微软标准顺序
  const params = new URLSearchParams({
    client_id: config.clientId,
    response_type: 'code',
    redirect_uri: config.redirectUri,
    response_mode: 'query',
    scope: config.scopes.join(' '),
    state: state,
    code_challenge: codeChallenge,
    code_challenge_method: 'plain', // 简化版本，生产环境应使用 'S256'
    prompt: 'select_account' // 强制显示账户选择
  });

  // 正确的URL结构：先调用租户，再使用客户端参数
  const authUrl = `https://login.microsoftonline.com/${config.tenantId}/oauth2/v2.0/authorize?${params.toString()}`;

  // console.log('Microsoft Auth URL 构建完成:', {
  //   tenantId: config.tenantId,
  //   clientId: config.clientId,
  //   redirectUri: config.redirectUri,
  //   scopes: config.scopes,
  //   authUrl
  // });

  return {
    authUrl,
    state,
    codeVerifier
  };
}

/**
 * 验证微软认证配置
 */
export function validateMicrosoftAuthConfig(): boolean {
  try {
    const config = getMicrosoftAuthConfig();
    
    // 检查必需字段
    const requiredFields = ['clientId', 'tenantId', 'redirectUri', 'scopes'];
    for (const field of requiredFields) {
      if (!config[field]) {
        console.error(`微软认证配置缺少字段: ${field}`);
        return false;
      }
    }

    // 检查租户ID格式
    if (config.tenantId === 'common') {
      console.warn('警告：使用 "common" 租户可能导致认证问题，建议使用具体的租户ID');
    }

    // 检查重定向URI格式
    try {
      new URL(config.redirectUri);
    } catch (error) {
      console.error('重定向URI格式无效:', config.redirectUri);
      return false;
    }

    return true;
  } catch (error) {
    console.error('微软认证配置验证失败:', error);
    return false;
  }
}
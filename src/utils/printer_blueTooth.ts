import Taro from '@tarojs/taro'
import { encodeGBK } from './gbkEncode'

// 打印机配置（需根据实际设备修改）
const printerConfig = {
  deviceName: '9EED', // 目标打印机名称
}

class PrinterService {
  private deviceId: string = ''
  private diviceList: Array<any> = []
  private isBluetoothInitialized: boolean = false
  private isDevTool: boolean = false
  private deviceFound: boolean = false
  private isIOS: boolean = false
  private serviceId: string = ''
  private characteristicId: string = ''

  constructor() {
    // 检测是否在开发者工具中运行
    try {
      const systemInfo = Taro.getSystemInfoSync()
      this.isDevTool = systemInfo.platform === 'devtools'
      console.log('运行环境:', this.isDevTool ? '开发者工具' : '真机')
      this.isIOS = Taro.getSystemInfoSync().platform === 'ios'
    } catch (e) {
      console.error('获取系统信息失败', e)
    }
  }

  // 初始化蓝牙适配器
  async initBluetooth() {
    // 如果已经初始化过，直接返回
    if (this.isBluetoothInitialized) {
      console.log('蓝牙适配器已经初始化过')
      return
    }

    // iOS专属权限请求
    if (this.isIOS) {
      try {
        const authStatus = await Taro.getSetting()
        console.log('iOS蓝牙权限状态:', authStatus.authSetting['scope.bluetooth'])
        if (!authStatus.authSetting['scope.bluetooth']) {
          const authRes = await Taro.authorize({ scope: 'scope.bluetooth' })
          console.log('iOS蓝牙授权结果:', authRes)
        }
      } catch (error) {
        console.error('iOS蓝牙权限请求失败:', error)
        throw new Error('请授权蓝牙权限')
      }
    }

    try {
      console.log('开始初始化蓝牙-------')
      
      // 先尝试打开蓝牙适配器
      try {
        const res = await Taro.openBluetoothAdapter()
        console.log('打开蓝牙适配器结果:', res)
        this.isBluetoothInitialized = true
      } catch (error) {
        // 处理"already opened"错误
        if (error.errMsg && error.errMsg.includes('already opened')) {
          console.log('蓝牙适配器已经打开')
          this.isBluetoothInitialized = true
        } else {
          throw error
        }
      }

      // 打开适配器后再获取状态
      try {
        const bluetoothState = await Taro.getBluetoothAdapterState()
        console.log('蓝牙适配器状态:', bluetoothState)
        
        if (!bluetoothState.available) {
          throw new Error('蓝牙适配器不可用')
        }
      } catch (error) {
        console.error('获取蓝牙状态失败:', error)
        // 如果获取状态失败，但适配器已经打开，我们仍然可以继续
        if (!this.isBluetoothInitialized) {
          throw error
        }
      }

      // 监听蓝牙适配器状态变化
      Taro.onBluetoothAdapterStateChange((res) => {
        console.log('蓝牙适配器状态变化:', res)
        if (!res.available) {
          this.isBluetoothInitialized = false
          Taro.showToast({ title: '蓝牙已断开，请重新连接', icon: 'none' })
        }
      })

      console.log('蓝牙适配器初始化完成')
    } catch (error) {
      console.error('蓝牙初始化错误:', error)
      Taro.showToast({ 
        title: error.errMsg?.includes('already opened') 
          ? '蓝牙已打开' 
          : (error.message || '请打开手机蓝牙'), 
        icon: 'none' 
      })
      throw error
    }
  }

  // 搜索设备
  async searchPrinter(orderData) {
    console.log('searchPrinter----start')
    try {
      await this.initBluetooth()
      this.deviceFound = false
      
      // 先停止之前的搜索
      try {
        await Taro.stopBluetoothDevicesDiscovery()
        console.log('已停止之前的蓝牙搜索')
      } catch (e) {
        console.log('停止之前的搜索失败，可能没有正在进行的搜索:', e)
      }
      
      // 确保开始搜索设备
      try {
        // 针对 iOS 的特殊处理
        const searchOptions = this.isIOS ? {
          allowDuplicatesKey: true, // 允许重复发现设备
          services: [], // iOS 上先不限制服务，尝试搜索所有设备
          interval: 0, // 设置搜索间隔为0，提高搜索频率
        } : {
          allowDuplicatesKey: false,
          services: [], // 不指定服务，搜索所有设备
        };

        console.log('开始搜索蓝牙设备，搜索参数:', searchOptions)
        await Taro.startBluetoothDevicesDiscovery(searchOptions)
        
        // 增加搜索等待时间，给设备更多被发现的机会
        await new Promise(resolve => setTimeout(resolve, this.isIOS ? 5000 : 3000))
        
        // 获取已发现的设备
        try {
          const connectedDevices = await Taro.getBluetoothDevices()
          console.log('已发现的蓝牙设备数量:', connectedDevices.devices?.length || 0)
          console.log('已发现的蓝牙设备详情:', JSON.stringify(connectedDevices.devices, null, 2))
          
          // 确保devices存在且是数组
          if (connectedDevices?.devices?.length > 0) {
            // 检查是否有匹配的设备
            for (const device of connectedDevices.devices) {
              const deviceName = device.name || device.localName || '';
              const deviceId = device.deviceId || '';
              console.log('检查设备:', { deviceName, deviceId, device })
              
              if (deviceName.includes(printerConfig.deviceName) || 
                  deviceId.includes(printerConfig.deviceName) ||
                  (this.isIOS && deviceName.toLowerCase().includes(printerConfig.deviceName.toLowerCase()))) {
                console.log('找到目标设备:', device)
                this.deviceFound = true
                this.deviceId = device.deviceId
                
                try {
                  await this.connectPrinter()
                  await this.printInvoice(orderData)
                  return true
                } catch (error) {
                  console.error('连接或打印已知设备失败:', error)
                  // 如果连接已知设备失败，继续搜索新设备
                  break
                }
              }
            }
          } else {
            console.log('没有发现任何蓝牙设备')
          }
        } catch (error) {
          console.error('获取蓝牙设备列表失败:', error)
        }
      } catch (error) {
        console.error('启动设备发现失败:', error)
      }
      
      return new Promise((resolve) => {
        // 设置超时
        let searchTimeout = setTimeout(() => {
          console.log('搜索超时，未找到打印机')
          Taro.stopBluetoothDevicesDiscovery()
          Taro.offBluetoothDeviceFound(deviceFoundHandler) // 修复：传入回调函数
          if (!this.deviceFound) {
            Taro.showToast({ title: '未找到打印机，请检查设备是否开启并处于可发现状态', icon: 'none' })
            resolve(false)
          }
        }, 15000) // 15秒超时
    
        // 监听发现新设备
        const deviceFoundHandler = async (res) => {
          if (this.deviceFound) return; // 如果已经找到设备，忽略后续回调
          
          // 确保devices存在且是数组
          const devices = res.devices || [res.device];
          if (!devices || !Array.isArray(devices) || devices.length === 1&&!devices[0].name) {
            // console.log('设备发现回调格式不正确:', res)
            return;
          }
          
          // console.log('发现新设备数量:', devices.length)
          // console.log('新设备详情:', JSON.stringify(devices, null, 2))
          
          for (const device of devices) {
            if (!device) continue; // 跳过无效设备
            
            const deviceName = device.name || device.localName || '';
            const deviceId = device.deviceId || '';
            // console.log('检查新设备:', { deviceName, deviceId, device })
            
            if (deviceName.includes(printerConfig.deviceName) || 
                deviceId.includes(printerConfig.deviceName) ||
                (this.isIOS && deviceName.toLowerCase().includes(printerConfig.deviceName.toLowerCase()))) {
              console.log('找到目标设备:', device)
              this.deviceFound = true
              clearTimeout(searchTimeout) // 清除超时
              Taro.stopBluetoothDevicesDiscovery()
              Taro.offBluetoothDeviceFound(deviceFoundHandler) // 修复：传入回调函数
              
              this.deviceId = device.deviceId
              this.diviceList.push(device.deviceId)
              
              try {
                await this.connectPrinter()
                await this.printInvoice(orderData)
                resolve(true)
                return;
              } catch (error) {
                console.error('连接或打印失败:', error)
                Taro.showToast({ title: `连接失败: ${error.message}`, icon: 'none' })
                resolve(false)
                return;
              }
            }
          }
        };
        
        // 注册监听
        Taro.onBluetoothDeviceFound(deviceFoundHandler);
      })
    } catch (error) {
      console.error('搜索打印机过程出错:', error)
      Taro.showToast({ title: error.message || '搜索打印机失败', icon: 'none' })
      return false
    }
  }

  // 连接打印机
  async connectPrinter() {
    console.log('connectPrinter----start', this.deviceId)

    if (!this.deviceId) {
      console.log('未找到设备--', this.deviceId, '--')
    }

    await Taro.createBLEConnection({
      deviceId: this.deviceId,
      timeout: 5000,
    })
    console.log('设备已连接')

    // 获取所有服务
    const services = await Taro.getBLEDeviceServices({ deviceId: this.deviceId })
    console.log('设备所有服务:', services.services.map(s => s.uuid))

    // 遍历所有服务，查找第一个有 write 权限的特征值
    let found = false
    for (const service of services.services) {
      const chars = await Taro.getBLEDeviceCharacteristics({
        deviceId: this.deviceId,
        serviceId: service.uuid,
      })
      console.log(`服务 ${service.uuid} 的所有特征值:`, chars.characteristics.map(c => ({ uuid: c.uuid, properties: c.properties })))
      const writeChar = chars.characteristics.find(c => c.properties.write)
      if (writeChar) {
        this.serviceId = service.uuid
        this.characteristicId = writeChar.uuid
        found = true
        break
      }
    }
    if (!found) throw new Error('未找到可写特征')
    console.log('选中的 serviceId:', this.serviceId, 'characteristicId:', this.characteristicId)
  }

  // 生成打印指令
  generatePrintData(content: string) {
    const cmd = [
      '\x1B\x40', // 初始化打印机
      '\x1B\x21\x00', // 普通字体，保证中英文大小一致
      `${content}\n\n\n`,
      '\x1D\x56\x41\x03', // 切纸指令
    ].join('');
    return encodeGBK(cmd);
  }

  // 发送打印数据
  async printInvoice(orderInfo) {
    const content = this.formatInvoiceContent(orderInfo)
    const buffer = this.generatePrintData(content)

    await Taro.writeBLECharacteristicValue({
      deviceId: this.deviceId,
      serviceId: this.serviceId,
      characteristicId: this.characteristicId,
      value: buffer.buffer,
    })
    console.log('打印指令已发送')
  }

  // 格式化票据内容
  private formatInvoiceContent(order) {
    return `
  	订单号：${order.no}
    时间：${order.time}
    --------------------------
    商品名称   单价   数量
    ${order.items.map((i) => `${i.name}   ${i.price}   ${i.qty}`).join('\n')}
    --------------------------
    合计：￥${order.total}
    `
  }

  // 完整流程调用
  async print(orderData) {
    try {
      console.log('orderData', orderData)

      const result = await this.searchPrinter(orderData)
      if (!result && !this.deviceFound) {
        Taro.showToast({ title: '未找到打印机设备', icon: 'none' })
      }
    } catch (error) {
      Taro.showToast({ title: `打印失败: ${error.message}`, icon: 'none' })
    }
  }
}

export default PrinterService

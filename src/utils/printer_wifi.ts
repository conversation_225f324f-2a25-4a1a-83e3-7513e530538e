import Taro from '@tarojs/taro';

// WiFi 打印机配置
const printerConfig = {
  ip: '*************', // 打印机局域网IP
  port: 9100,          // 常见端口：9100/8008
  timeout: 5000
}

class WiFiPrinterService {
  private socketTask: Taro.SocketTask | null = null;

  // 建立 TCP 连接
  async connect() {
    if (this.socketTask) return;

    this.socketTask = Taro.createTCPSocket();
    
    // 监听连接
    this.socketTask.onOpen(() => {
      console.log('Socket 已连接');
    });

    // 监听错误
    this.socketTask.onError((err) => {
      console.error('连接错误:', err);
      this.close();
      throw new Error('连接打印机失败');
    });

    // 发起连接
    await this.socketTask.connect({
      address: printerConfig.ip,
      port: printerConfig.port,
      timeout: printerConfig.timeout
    });
  }

  // 生成打印指令（与蓝牙方案相同）
  generatePrintData(content: string) {
    const encoder = new TextEncoder();
    const cmd = [
      '\x1B\x40', // 初始化
      '\x1B\x21\x30',
      `${content}\n\n\n`,
      '\x1D\x56\x41\x03' // 切纸
    ].join('');
    return encoder.encode(cmd);
  }

  // 发送数据
  async sendData(buffer: Uint8Array) {
    if (!this.socketTask) throw new Error('未连接打印机');
    
    // 分片发送（建议 1024 bytes/片）
    const chunkSize = 1024;
    for (let i=0; i<buffer.length; i+=chunkSize) {
      const chunk = buffer.slice(i, i+chunkSize);
      await this.socketTask.write({
        data: chunk.buffer
      });
    }
  }

  // 关闭连接
  close() {
    if (this.socketTask) {
      this.socketTask.close();
      this.socketTask = null;
    }
  }

  // 完整打印流程
  async printInvoice(orderInfo: any) {
    try {
      await this.connect();
      const content = this.formatInvoiceContent(orderInfo);
      const buffer = this.generatePrintData(content);
      await this.sendData(buffer);
      Taro.showToast({ title: '打印成功' });
    } catch (err) {
      Taro.showToast({ title: `打印失败: ${err.message}`, icon: 'none' });
    } finally {
      this.close();
    }
  }

  // 格式化内容（与蓝牙方案相同）
  private formatInvoiceContent(order: any) {
    return `
      订单号：${order.no}
      时间：${order.time}
      --------------------------
      商品名称   单价   数量
      ${order.items.map((i: any) => `${i.name}   ${i.price}   ${i.qty}`).join('\n')}
      --------------------------
      合计：￥${order.total}
    `;
  }
}



export default WiFiPrinterService

import { useState } from "react";

const useDebounce = (fun: (v:any) => any, delay = 1000) => {
  const [loading,setLoading] = useState(false)
  let timer: NodeJS.Timeout | null = null;
  const debounce = (v) => {
    if (loading) return;
    timer = setTimeout(() => {
      setLoading(true);
      try {
        fun(v);
      } catch (error) {
        setLoading(false);
      }
    }, delay);
  };

  return [loading, debounce];
};

export default useDebounce;

import Taro from '@tarojs/taro'
import { useEffect, useState } from 'react'

/**
 * 获取底部安全区域高度的 Hook
 * @returns {Object} 包含 safeAreaBottom 的对象
 */
const useSafeAreaBottom = () => {
  const [safeAreaBottom, setSafeAreaBottom] = useState<number>(0)

  useEffect(() => {
    try {
      // 获取系统信息
      const systemInfo = Taro.getSystemInfoSync()
      
      // 计算底部安全区域高度
      let bottomSafeHeight = 0
      
      if (systemInfo.safeArea) {
        // 屏幕高度 - 安全区域底部 = 底部安全区域高度
        bottomSafeHeight = systemInfo.screenHeight - systemInfo.safeArea.bottom
      }
      
      // 对于 iPhone X 及以后的机型，如果没有获取到安全区域信息，使用默认值
      if (bottomSafeHeight === 0 && systemInfo.model && systemInfo.model.includes('iPhone')) {
        // iPhone X 及以后机型的底部安全区域通常是 34px
        const isIPhoneX = systemInfo.screenHeight >= 812 // iPhone X 及以后的屏幕高度
        if (isIPhoneX) {
          bottomSafeHeight = 34
        }
      }
      
      console.log('底部安全区域计算结果:', {
        '底部安全区域高度': bottomSafeHeight,
        '系统信息': {
          screenHeight: systemInfo.screenHeight,
          safeArea: systemInfo.safeArea,
          model: systemInfo.model,
          platform: systemInfo.platform
        }
      })
      
      setSafeAreaBottom(bottomSafeHeight)
      
    } catch (error) {
      console.error('获取底部安全区域失败:', error)
      setSafeAreaBottom(0)
    }
  }, [])

  return {
    safeAreaBottom,
    // 提供 CSS 变量名
    cssVariableName: '--safe-area-bottom'
  }
}

export default useSafeAreaBottom

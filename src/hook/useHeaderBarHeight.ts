import Taro, { useDidShow } from '@tarojs/taro'
import { useStores } from '@/hook'
import { useEffect, useState, useCallback } from 'react'
import useIsCurrentPage from './useIsCurrentPage'

/**
 * 获取 HeaderBar 高度的 Hook
 * @returns {Object} 包含 headerBarHeight 的对象
 */
const useHeaderBarHeight = () => {
  const { commonStore } = useStores()
  const { isCurrentPage } = useIsCurrentPage()
  const [headerBarHeight, setHeaderBarHeight] = useState<number>(0)

  // 计算实际的 HeaderBar 高度
  const calculateHeight = useCallback(async () => {
    try {
      // 获取系统信息
      const systemInfo = Taro.getSystemInfoSync()
      const statusBarHeight = systemInfo.statusBarHeight || 0

      // 尝试多次查询 HeaderBar 高度，因为可能需要等待渲染
      let headerBarHeight = 0
      let retryCount = 0
      const maxRetries = 10

      const queryHeaderBar = async (): Promise<number> => {
        return new Promise((resolve) => {
          const query = Taro.createSelectorQuery()
          query.select('.header_bar').boundingClientRect((rect) => {
            console.log('查询 HeaderBar 结果:', rect, '重试次数:', retryCount)
            // rect 可能是数组，取第一个元素
            const rectData = Array.isArray(rect) ? rect[0] : rect
            if (rectData && rectData.height && rectData.height > 0) {
              resolve(rectData.height)
            } else {
              resolve(0)
            }
          }).exec()
        })
      }

      // 重试查询直到获取到高度或达到最大重试次数
      while (headerBarHeight === 0 && retryCount < maxRetries) {
        headerBarHeight = await queryHeaderBar()
        if (headerBarHeight === 0) {
          retryCount++
          // 等待一段时间再重试
          await new Promise(resolve => setTimeout(resolve, 100))
        }
      }

      if (headerBarHeight === 0) {
        // 如果还是没有获取到，使用默认值：状态栏高度 + 导航栏高度
        console.warn('无法获取 HeaderBar 高度，使用默认值')
        headerBarHeight = statusBarHeight + 44
      }

      // HeaderBar 的高度已经包含了状态栏高度，所以直接使用查询到的高度
      const totalHeight = headerBarHeight

      console.log('HeaderBar 高度计算结果:', {
        '查询到的 HeaderBar 总高度': headerBarHeight,
        '状态栏高度': statusBarHeight,
        '最终使用的高度': totalHeight,
        '重试次数': retryCount,
        '系统信息': {
          statusBarHeight: systemInfo.statusBarHeight,
          platform: systemInfo.platform,
          model: systemInfo.model
        }
      })

      setHeaderBarHeight(totalHeight)

      // 同时更新到 commonStore
      if (commonStore.fixedHeight) {
        commonStore.fixedHeight.header = totalHeight
      }

    } catch (error) {
      console.error('计算 HeaderBar 高度失败:', error)
      // 使用默认值：状态栏高度 + 导航栏高度
      const fallbackHeight = (Taro.getSystemInfoSync().statusBarHeight || 0) + 44
      setHeaderBarHeight(fallbackHeight)
    }
  }, [commonStore])

  // 页面显示时重新计算
  useDidShow(() => {
    if (isCurrentPage()) {
      // 延迟一下确保 DOM 已经渲染
      setTimeout(() => {
        calculateHeight()
      }, 100)
    }
  })

  useEffect(() => {
    calculateHeight()
  }, [calculateHeight])

  return {
    headerBarHeight,
    // 提供一个获取当前高度的方法
    getHeaderBarHeight: () => headerBarHeight,
    // 提供一个重新计算的方法
    recalculate: calculateHeight,
    // 提供 CSS 变量名，方便在样式中使用
    cssVariableName: '--headerbar-height'
  }
}

export default useHeaderBarHeight

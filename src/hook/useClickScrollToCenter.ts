import { useState } from 'react'

import useUUIDSelect from './useUUIDSelect'

interface useClickScrollToCenterParam {
  itemClass: string
}
const useClickScrollToCenter = (props: useClickScrollToCenterParam) => {
  const { uuid, select, selectAll } = useUUIDSelect()
  const [scrollLeft, setScrollLeft] = useState(0)

  const scrollToCenter = async (index: number) => {
    if (index < 0) {
      return
    }
    try {
      const res = await select()
      const res2 = await selectAll(props.itemClass)
      if (res && res2?.length && res2[index]) {
        if (!res.width) {
          //说明位于后台或者display:none
          return
        }
        //离左边距离
        let leftDistance = (res2[index]?.left || 0) - (res2[0]?.left || 0)
        //需要滚动到中间
        leftDistance += (res2[index]?.width || 0) / 2 - res.width / 2
        if (leftDistance < 0) {
          leftDistance = 0
        }
        setScrollLeft(leftDistance)
      } else {
        console.log(`useClickScrollToCenter empty:\n${props.itemClass}`, res, res2)
      }
    } catch (e) {
      console.log(`useClickScrollToCenter error:\n${props.itemClass}`, e)
    }
  }
  return { uuid, scrollLeft, scrollToCenter }
}
export default useClickScrollToCenter

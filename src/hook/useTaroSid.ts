import { MutableRefObject, useEffect, useRef } from 'react'
import Taro from '@tarojs/taro'

//帮助选择元素hook
//返回taroSid用于识别
// 如果会出现不渲染的情况不要用 使用useUUIDSelect taro会有问题不更新ref
interface UseTaroSidResult {
  sid: MutableRefObject<string>
  ref: MutableRefObject<any>
  select: (param?: string) => Promise<any>
  selectAll: (param?: string) => Promise<any>
  selectNode: (param?: string) => Promise<any>
}
const useTaroSid = (): UseTaroSidResult => {
  const sid = useRef('')
  const ref = useRef()

  useEffect(() => {
    const { current } = ref
    sid.current = current ? `${(current as any).sid}` : ''
  }, [ref.current])

  const select = async (param = '') => {
    return new Promise((resolve) => {
      Taro.createSelectorQuery()
        .select(`#${sid.current} ${param}`)
        ?.boundingClientRect()
        .exec((res) => {
          resolve(res && res[0])
        })
    })
  }
  const selectAll = async (param = '') => {
    return new Promise((resolve) => {
      Taro.createSelectorQuery()
        .selectAll(`#${sid.current} ${param}`)
        ?.boundingClientRect()
        .exec((res) => {
          resolve(res && res[0])
        })
    })
  }

  const selectNode = async (param = '') => {
    return new Promise((resolve) => {
      Taro.createSelectorQuery()
        .select(`#${sid.current} ${param}`)
        .fields({ node: true, size: true })
        .exec(async (res) => {
          if (res?.length && res[0].node) {
            resolve(res[0].node)
          } else {
            console.error('selectNode error:', res)
          }
        })
    })
  }

  return { sid, ref, select, selectAll, selectNode }
}
export default useTaroSid

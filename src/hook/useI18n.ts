import useStores from "./useStores";
import { Language } from "@/i18n";

/**
 * 国际化 Hook
 * 提供翻译函数和语言切换功能
 */
export const useI18n = () => {
  const { i18nStore } = useStores();

  return {
    // 翻译函数
    t: (key: string, params?: Record<string, string | number>) => i18nStore.t(key, params),

    // 当前语言
    currentLanguage: i18nStore.currentLanguage,

    // 设置语言
    setLanguage: (language: Language) => i18nStore.setLanguage(language),

    // 切换语言
    toggleLanguage: () => i18nStore.toggleLanguage(),

    // 获取支持的语言列表
    getSupportedLanguages: () => i18nStore.getSupportedLanguages(),
  };
};

export default useI18n;

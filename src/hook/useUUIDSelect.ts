import { useState } from 'react'
import Taro from '@tarojs/taro'

import { generateUUID } from '@/utils'

//帮助选择元素hook
interface useUUIDSelectResult {
  uuid: string
  select: (param?: string) => Promise<any>
  selectAll: (param?: string) => Promise<any>
  selectNode: (param?: string) => Promise<any>
}
const useUUIDSelect = (): useUUIDSelectResult => {
  //用作class不可是数字开头
  const [uuid] = useState<string>('_' + generateUUID())

  const select = async (param = '') => {
    return new Promise((resolve) => {
      Taro.createSelectorQuery()
        .select(`.${uuid} ${param}`)
        ?.boundingClientRect()
        .exec((res) => {
          resolve(res && res[0])
        })
    })
  }
  const selectAll = async (param = '') => {
    return new Promise((resolve) => {
      Taro.createSelectorQuery()
        .selectAll(`.${uuid} ${param}`)
        ?.boundingClientRect()
        .exec((res) => {
          resolve(res && res[0])
        })
    })
  }

  const selectNode = async (param = '') => {
    return new Promise((resolve) => {
      Taro.createSelectorQuery()
        .select(`.${uuid} ${param}`)
        .fields({ node: true, size: true })
        .exec(async (res) => {
          if (res?.length && res[0].node) {
            resolve(res[0].node)
          } else {
            console.error('selectNode error:', res)
          }
        })
    })
  }

  return { uuid, select, selectAll, selectNode }
}
export default useUUIDSelect

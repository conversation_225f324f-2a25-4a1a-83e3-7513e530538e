import { <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>View, Icon } from "@tarojs/components";
import { observer } from "mobx-react";
import { useRef, useState, useCallback } from "react";
import SearchInput from "@/components/SearchInput";

import { MainLayout } from "@/components";
import { useStores, useI18n } from "@/hook";
import { CustomerInfo } from "@/mobx/model/UserInfo";
import http from "@/http";
import toastStore from "@/mobx/store/toast";

import "./index.scss";
import { toJump, backPage } from "@/utils";
import IconFont from "@/components/iconfont";

const CustomerPage = () => {
  const layoutRef = useRef<any>();
  const { customerStore } = useStores();
  const { t } = useI18n();
  const [searchValue, setSearchValue] = useState("");
  const [customerList, setCustomerList] = useState<CustomerInfo[]>([]);

  // 分页相关状态
  const [currentPage, setCurrentPage] = useState(1);
  const [totalCount, setTotalCount] = useState(0);
  const [hasMore, setHasMore] = useState(false);
  const [isSearching, setIsSearching] = useState(false);
  const [beforeSearch, setBeforeSearch] = useState(true);
  const pageSize = 10;

  const debounceTimer = useRef<NodeJS.Timeout | null>(null);

  // 搜索客户的函数
  const searchCustomers = useCallback(
    async (fuzzy: string, page: number = 1, isLoadMore: boolean = false) => {
      if (!fuzzy.trim()) {
        setCustomerList([]);
        setCurrentPage(1);
        setTotalCount(0);
        setHasMore(false);
        return;
      }

      setIsSearching(true);

      if (!isLoadMore) {
        setCurrentPage(1);
        setCustomerList([]);
      }

      try {
        // 调用API接口
        const response: any = await http.customer.searchCustomers({
          fuzzy,
          page: page,
          page_size: pageSize,
        });
        setBeforeSearch(false);

        if (response.code === 0) {
          const newData = response.data?.list || [];
          const total = response.data?.count || 0;
          // console.log("response.data", response.data);

          if (isLoadMore) {
            // 加载更多：追加数据
            setCustomerList((prev) => [...prev, ...newData]);
          } else {
            // 新搜索：替换数据
            setCustomerList(newData);
          }

          setTotalCount(total);
          setCurrentPage(page);

          // 计算是否还有更多数据
          const currentTotal = isLoadMore ? customerList.length + newData.length : newData.length;
          setHasMore(currentTotal < total);
          // console.log("currentTotal<total", currentTotal < total);
        } else {
          toastStore.show(response.msg || t("customer.searchFailed"));
          if (!isLoadMore) {
            setCustomerList([]);
          }
        }
      } catch (error) {
        // console.error("搜索顾客失败:", error);
        toastStore.show(t("customer.searchFailedRetry"));
        if (!isLoadMore) {
          setCustomerList([]);
        }
      } finally {
        setIsSearching(false);
      }
    },
    [pageSize] // 不要依赖 customerList.length
  );

  // 处理搜索输入变化
  const handleSearchChange = useCallback(
    (value: string) => {
      setSearchValue(value);

      if (debounceTimer.current) {
        clearTimeout(debounceTimer.current);
      }

      debounceTimer.current = setTimeout(async () => {
        if (value.length >= 4) {
          await searchCustomers(value, 1, false);
        } else {
          setCustomerList([]);
          setCurrentPage(1);
          setTotalCount(0);
          setHasMore(false);
        }
      }, 300); // 300ms 防抖，只有停止输入300ms后才触发搜索
    },
    [searchCustomers] // 只依赖 searchCustomers
  );

  // 滚动到底部加载更多
  const handleScrollToLower = useCallback(async () => {
    if (hasMore && !isSearching && searchValue.length >= 4) {
      await searchCustomers(searchValue, currentPage + 1, true);
    }
  }, [hasMore, isSearching, searchValue, currentPage, searchCustomers]);


  return (
    <MainLayout ref={layoutRef} className="customer_page" showTopPlaceholder={false} headerType="withBack">
      <SearchInput placeholder={t("customer.searchPlaceholder")} onChange={handleSearchChange} clearable />
      {/* 搜索结果区域 */}
      {beforeSearch ? (
        <View className="h-238"></View>
      ) : (
        <ScrollView
          className={`search-results bg-white p-8 rounded-lg ${beforeSearch ? "h-238" : "h-288"} `}
          style={{ width: `calc(100vw - 32px)` }}
          scrollWithAnimation
          scrollY
          onScrollToLower={handleScrollToLower}
          lowerThreshold={50}
          type="list">
          {customerList.length > 0 ? (
            <>
              {customerList.map((customer, index) => (
                <View key={customer.uid || index} className={`h-20 py-8 border-b-1`} onClick={() => toJump(`subpackages/customer/detail/index?id=${customer.uid}&mobile=${customer.mobile}`)}>
                  <View className="flex flex-col ml-4">
                    <View className="text-base font-medium text-brand-2D2E2C mb-2">
                      {customer.first_name} {customer.last_name}
                    </View>
                    <View className="text-sm text-brand-2D2E2C">
                      {customer.mobile} {customer.email || t("customer.noEmailShort")}
                    </View>
                  </View>
                </View>
              ))}
            </>
          ) : (
            <View className="text-center py-8 flex flex-col items-center justify-center h-full" style={{ width: `calc(100vw - 32px)` }}>
              {/* 无搜索结果提示 */}
              <IconFont name="error" size={32}></IconFont>
              <View className="text-brand-2D2E2C text-base font-medium leading-10 pt-4">{t("customer.noCustomersFound")}</View>
            </View>
          )}
        </ScrollView>
      )}

      {/* 默认展示按钮 */}
      <View className="mt-auto">
        {/* {beforeSearch ? (
          <View className="flex items-center justify-center">
            <View className="">
              <Button
                className="btn-black mb-10"
                onClick={() => {
                  customerStore.switchGuest(backPage);
                }}
                loading={isSearching}>
                {t("customer.guest")}
              </Button>
            </View>
          </View>
        ) : ( */}
        <View className="flex items-center justify-center mt-auto px-8" style={{ width: `calc(100vw - 32px)` }}>
          {/* 搜索时展示按钮 */}
          {/* <Button
            className="btn-white mb-10 flex-1 mr-5"
            onClick={() => {
              customerStore.switchGuest(backPage);
            }}
            loading={isSearching}>
            {t("customer.guest")}
          </Button> */}
          <Button
            className="btn-black mb-10 flex-1 ml-5"
            onClick={() => {
							//searchValue满足手机号码11位才代参
              if (searchValue.length === 11) {
                toJump(`subpackages/customer/registration/index?mobile=${searchValue}`);
              }else{
                toJump(`subpackages/customer/registration/index`);
							}
            }}>
            {t("customer.registration")}
          </Button>
        </View>
        {/* )} */}
      </View>
    </MainLayout>
  );
};
export default observer(CustomerPage);

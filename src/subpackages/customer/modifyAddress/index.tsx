import { MainLayout } from "@/components";
import { View } from "@tarojs/components";
import { Radio, Divider } from "@nutui/nutui-react-taro";
import IconFont from "@/components/iconfont";
import sty from "./index.modules.scss";
import { SafeAreaBottom } from "@/components";
import Taro from "@tarojs/taro";

const ModifyAddress = () => {

  const onAddAddress = () => {
    Taro.navigateTo({
      url:"/subpackages/customer/editAddress/index"
    })

  }
  return (
    <MainLayout
      initOptions={{
        inited: false,
        initLoading: false,
        loadingFullScreen: true
      }}
      // 使用便捷配置方式
      headerType="withBack"
      headerBackgroundColor="#fafafa"
      style={{
        backgroundSize: "100% auto",
        backgroundColor: "#fafafa"
      }}
      showBottomPlaceholder={false}
    >
      <View className={sty.modifyAddressPage}>
        <View className={sty.addressContent}>
          <View className={sty.content}>

          

          <View className={sty.rowTitle}>Address Book</View>
          <Radio.Group
            defaultValue="1"
            labelPosition="left"
            style={{ width: "100%" }}
          >
            <Radio
              icon={<IconFont name="checkbox" size={20} />}
              activeIcon={<IconFont name="checked" size={20} />}
              value="1"
            >
              <View>
                <View className={sty.card}>
                  <View className={sty.name}>Wang, Jason</View>
                  <View className={sty.phone}>15000000000</View>
                  <View className={sty.area}>
                    XuHui District,Shanghai 169 Anfu Road XuHui District
                  </View>
                  <View className={sty.operateArea}>
                    <View className={sty.btn}>
                      <IconFont name="bianji" size={16} />
                      <View className={sty.text}>Edit</View>
                    </View>
                    <View className={sty.btn}>
                      <IconFont name="bianji" size={16} />
                      <View className={sty.text}>Delete</View>
                    </View>
                  </View>
                </View>
                {/* <Divider /> */}
              </View>
            </Radio>

            <Radio
              icon={<IconFont name="checkbox" size={20} />}
              activeIcon={<IconFont name="checked" size={20} />}
              value="2"
            >
              <View>
                <View className={sty.card}>
                  <View className={sty.name}>Wang, Jason</View>
                  <View className={sty.phone}>15000000000</View>
                  <View className={sty.area}>
                    XuHui District,Shanghai 169 Anfu Road XuHui District
                  </View>
                  <View className={sty.operateArea}>
                    <View className={sty.btn}>
                      <IconFont name="bianji" size={16} />
                      <View className={sty.text}>Edit</View>
                    </View>
                    <View className={sty.btn}>
                      <IconFont name="bianji" size={16} />
                      <View className={sty.text}>Delete</View>
                    </View>
                  </View>
                </View>
                {/* <Divider className={sty.lastAddress}/> */}
              </View>
            </Radio>
          </Radio.Group>
          </View>

        </View>

        <View className={sty.addBtn}>
          <View className={sty.btn} onClick={onAddAddress}>Add New Address</View>
          <SafeAreaBottom />
        </View>
      </View> 
    </MainLayout>
  );
};
export default ModifyAddress;

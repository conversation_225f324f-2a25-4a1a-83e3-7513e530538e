.modifyAddressPage {
  @apply flex flex-col h-full box-border p-8 pb-0;

  .addressContent {
    @apply flex-1;

    .content {
      @apply px-8 py-10 bg-white;

      .rowTitle {
        @apply text-md font-bold text-brand-2D2E2C mb-10;
      }
    }
  }

  .radioRow {
    background-color: pink;
    @apply py-10 px-8 box-border;
  }

  :global {
    .nut-radiogroup {
      .nut-radio {
        @apply m-0 justify-center items-start border-b-1 pb-12 mb-12;

        &:last-child {
          @apply border-0 pb-0;
        }

        .nut-radio-label {
          flex: 1;
        }
      }
    }

    .nut-divider {
      @apply my-12 border-brand-E1E1E1;
    }

    .lastAddress {
      display: none;
    }
  }

  .card {
    flex: 1;
    margin-right: 20px;

    .name {
      @apply font-medium text-base leading-8 text-brand-2D2E2C;
    }

    .phone {
      @apply mt-1 text-sm leading-8 text-brand-2D2E2C;
    }

    .area {
      @apply mt-4 text-sm leading-8 text-brand-898989;
    }

    .operateArea {
      @apply flex items-center text-sm leading-8 text-brand-2D2E2C mt-8;

      .btn {
        @apply flex items-center;

        &:first-child {
          @apply mr-16;
        }

        .text {
          @apply ml-2;
        }
      }
    }
  }

  .addBtn {
    .btn {
      @apply flex justify-center items-center text-sm leading-8 text-white btn-default;
    }
  }
}

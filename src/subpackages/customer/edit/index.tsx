import Taro from "@tarojs/taro";
import { View } from "@tarojs/components";
import { observer } from "mobx-react";
import { MainLayout, FormInput, TextareaField } from "@/components";
import { Button as NutButton, Form, TextArea } from "@nutui/nutui-react-taro";
import { useState, useCallback, useEffect } from "react";
import { useStores, useI18n } from "@/hook";
import { backPage } from "@/utils";
import { useRouter } from "@tarojs/taro";
import sty from "./index.module.scss";

const CustomerEditPage = () => {
  const router = useRouter();
  const { customerStore, customerDetail } = useStores();
  const { t } = useI18n();

  const [errors, setErrors] = useState({
    mobile: false,
    firstName: false,
    lastName: false,
    email: false,
    note: false,
  });

  const [noteValue, setNoteValue] = useState("");

  // 表单值状态 - 从 store 中读取当前客户信息
  const [formValues, setFormValues] = useState({
    mobile: "",
    firstName: "",
    lastName: "",
    email: "",
  });

  // 获取当前要编辑的客户数据的helper函数
  const getCurrentCustomer = () => {
    return customerDetail.detail && Object.keys(customerDetail.detail).length > 0
      ? customerDetail.detail
      : customerStore.currentCustomer;
  };

  // 初始化表单数据
  useEffect(() => {
    const customer = getCurrentCustomer();

    if (customer && Object.keys(customer).length > 0) {
      setFormValues({
        mobile: customer.mobile || "",
        firstName: customer.first_name || "",
        lastName: customer.last_name || "",
        email: customer.email || "",
      });
      setNoteValue(customer.note || "");
    }
  }, [customerDetail.detail, customerStore.currentCustomer]);

  // 检查必填项是否都已填写
  const isRequiredFieldsFilled = formValues.mobile && formValues.firstName && formValues.lastName;

  const clearError = useCallback((field: string) => {
    setErrors((prev) => {
      if (prev[field]) {
        return {
          ...prev,
          [field]: false,
        };
      }
      return prev;
    });
  }, []);

  const handleSubmit = async (formData: any) => {
    // console.log("Update customer data:", formData);

    // 获取当前要编辑的客户数据
    const currentCustomer = getCurrentCustomer();

    if (!currentCustomer?.uid) {
      Taro.showToast({
        title: "客户信息不存在",
        icon: "error",
      });
      return;
    }

    // 准备更新数据
    const updateData = {
      uid: currentCustomer.uid,
      mobile: formData.mobile,
      first_name: formData.firstName,
      last_name: formData.lastName,
      email: formData.email || "",
      note: noteValue,
    };

    // 调用 customerStore 的更新方法
    customerStore.updateCustomer(updateData, () => {
      backPage();
    });
  };

  const handleSubmitFailed = (_, errorFields: any) => {
    console.log("Form validation failed:", errorFields);
    Taro.showToast({
      title: "Please fill in required fields",
      icon: "error",
    });
  };

  return (
    <MainLayout
      initOptions={{
        inited: false,
        initLoading: false,
        loadingFullScreen: true,
      }}
      headerType="withBack"
      style={{
        backgroundSize: "100% auto",
        backgroundColor: "#FAFAFA",
      }}>
      <View className={["px-8", sty.formArea].join(" ")}>
        <View className="text-lg font-medium text-brand-2D2E2C mb-12 mt-10">Basic Info</View>

        <Form
          onFinish={handleSubmit}
          onFinishFailed={handleSubmitFailed}
          initialValues={{
            mobile: getCurrentCustomer()?.mobile || "",
            firstName: getCurrentCustomer()?.first_name || "",
            lastName: getCurrentCustomer()?.last_name || "",
            email: getCurrentCustomer()?.email || "",
          }}
          footer={
            <View className="mt-14 flex space-x-4 w-full">
              <NutButton type="default" className="btn-white py-0 flex-1 mr-5" onClick={() => backPage()}>
                {t("common.cancel")}
              </NutButton>
              <NutButton type="default" formType="submit" className="btn-black flex-1 ml-5">
                {t("common.save")}
              </NutButton>
            </View>
          }>
          <View className="space-y-6">
            {/* Phone Number */}
            <FormInput
              name="mobile"
              label={t("customer.mobile")}
              type="text"
              required
              showRequire
              value={getCurrentCustomer()?.mobile}
              rules={{
                required: { required: true, message: t("validation.phoneRequired") },
              }}
            />

            {/* First Name and Last Name */}
            <View className="flex space-x-4">
              <FormInput
                name="firstName"
                class_name="mr-2"
                label={t("customer.firstName")}
                required
                showRequire
                value={getCurrentCustomer()?.first_name}
                rules={{
                  required: { required: true, message: t("validation.firstNameRequired") },
                }}
              />
              <FormInput
                name="lastName"
                class_name="ml-2"
                label={t("customer.lastName")}
                required
                showRequire
                value={getCurrentCustomer()?.last_name}
                rules={{
                  required: { required: true, message: t("validation.lastNameRequired") },
                }}
              />
            </View>

            {/* Email */}
            <FormInput
              name="email"
              label={t("customer.email")}
              type="text"
              value={getCurrentCustomer()?.email}
              rules={{
                validator: (_: any, value: any, setShowVerify: any) => {
                  if (value && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) {
                    setShowVerify && setShowVerify(true);
                    return false;
                  }
                  setShowVerify && setShowVerify(false);
                  return true;
                },
              }}
            />

            {/* Note */}
            {/* <View className={`text-sm leading-8 mb-2`}>Note</View> */}
            <TextareaField
              label={t("registration.note")}
              // className={`w-full min-h-22 rounded px-8 py-4 bg-white border-1-area nut-input-text text-xs font-regular ${errors.note && "input-error"}`}
              // label={t('registration.note')}
              value={noteValue}
              onChange={setNoteValue}
              placeholder="Comments"
              maxLength={100}
            />
            {/* <TextArea
              className={`w-full min-h-22 rounded px-8 py-4 bg-white border-1-area nut-input-text text-xs font-regular ${errors.note && "input-error"}`}
              placeholder="Comments"
              placeholderClass="text-sm"
              value={noteValue}
              onChange={(value) => setNoteValue(value)}
              rows={4}
              maxLength={100}
            /> */}
          </View>
        </Form>
      </View>
    </MainLayout>
  );
};

export default observer(CustomerEditPage);

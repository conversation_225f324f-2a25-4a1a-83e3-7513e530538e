.detailPage {
  @apply flex flex-col h-full box-border;

  .content {
    @apply px-8 pt-8 bg-brand-FAFAFA flex-1 overflow-scroll bg-brand-FAFAFA;
  }

  .btnArea {
    @apply px-8 pt-8 shadow bg-white;

    .btn {
      @apply btn-default text-brand-2D2E2C text-base h-22 flex justify-center items-center text-white;
    }
  }

  :global {
    .customerCardCom {
      border: none;
    }

    .h5-textarea {
      font-family: "GTAmericaMedium" !important;
      font-size: 14px;
      line-height: 16px;





    }
  }

  .radioRow {
    :global {
      .nut-checkbox-label {
        margin-left: 0;
      }
    }




  }
}

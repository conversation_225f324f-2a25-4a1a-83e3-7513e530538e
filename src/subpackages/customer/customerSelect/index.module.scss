.customerSelectPage {
  height: 100%;
  box-sizing: border-box;
  :global {
    .nut-radiogroup {
      height: 100%;
      display: flex;
      flex-direction: column;

      .nut-radio {
        border: 1px solid #e1e1e1;
        border-radius: 4px;
        padding-right: 16px;
        margin: 0;
        .nut-radio-icon-checked,
        .nut-radio-icon {
          padding-top: 16px;
          align-self: flex-start;
        }

        .nut-radio-label {
          flex: 1;

          .customerCardCom {
            border: none;
          }
        }
      }
    }
  }

  .selectedRadio {
    border: 1px solid #2d2e2c !important;
  }

  .area {
    @apply flex-1 px-8 pb-24 box-border overflow-y-scroll;
  }

  .btns {
    position: fixed;
    left: 0;
    bottom: 0;
    width: 100%;
    box-sizing: border-box;
    background: #fff;
    box-shadow: 0px -2px 12px 0px #0000000a;
    z-index: 10;
  }


}

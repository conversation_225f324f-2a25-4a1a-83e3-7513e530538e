import { View } from "@tarojs/components";
import { MainLayout,CustomerCard } from "@/components";
import { Radio } from "@nutui/nutui-react-taro";
import { Checklist } from "@nutui/icons-react-taro";
import SafeAreaBottom from "@/components/SafeAreaBottom";
import sty from "./index.module.scss";
import IconFont from "@/components/iconfont";

import {useStores} from "@/hook";

const CustomerSelect = () => {
  const {userStore} = useStores()

  const {userInfo} = userStore

  return (
    <MainLayout
      initOptions={{
        inited: false,
        initLoading: false,
        loadingFullScreen: true
      }}
      // 使用便捷配置方式
      headerType="withBack"
      style={{
        backgroundSize: "100% auto",
        backgroundColor: "#fafafa"
      }}
    >
      <View className={sty.customerSelectPage}>
        <View className="flex flex-col bg-white h-full justify-between ">
          <Radio.Group
            defaultValue="1"
            labelPosition="left"
            style={{ width: "100%" }}
          >
            <View className="px-8 bg-white pt-8 pb-12">
              <View className="pt-8 pb-6 text-sm left-8 text-brand-2D2E2C font-gt-america-medium  ">
                Selected Customers
              </View>
              <Radio
                icon={<IconFont name="danxuan-1" size={20} />}
                activeIcon={<IconFont name="danxuan" size={20} />}
                value="1"
                className={sty.selectedRadio}
              >
                <CustomerCard info={userInfo as any }  />
              </Radio>
            </View>
            <View className="h-4 bg-brand-FAFAFA"></View>
            <View className="pt-12 bg-white flex-1 flex flex-col overflow-hidden">
              <View className="px-8 font-gt-america-norma text-sm text-brand-898989 leading-8">
                Customers Registered Today
              </View>
              <View className={sty.area}>
                <View className="mt-6">
                  <Radio
                    icon={<IconFont name="danxuan-1" size={20} />}
                    activeIcon={<IconFont name="danxuan" size={20} />}
                    value="2"
                  >
                    <CustomerCard info={userInfo as any }  />
                  </Radio>
                </View>
                <View className="mt-6">
                  <Radio
                    icon={<IconFont name="danxuan-1" size={20} />}
                    activeIcon={<IconFont name="danxuan" size={20} />}
                    value="2"
                  >
                    <CustomerCard  info={userInfo as any } />
                  </Radio>
                </View>
                <View className="mt-6">
                  <Radio
                    icon={<IconFont name="danxuan-1" size={20} />}
                    activeIcon={<IconFont name="danxuan" size={20} />}
                    value="3"
                  >
                    <CustomerCard  info={userInfo as any } />
                  </Radio>
                </View>
              </View>
            </View>
          </Radio.Group>
          <View className={[sty.btns, " px-8 pt-6"].join()}>
            <View className="flex justify-between w-full ">
              <View className="border-1-area flex-1 mr-6 flex justify-center items-center">
                Logout
              </View>
              <View className="btn-default flex-1 flex justify-center items-center">
                Confirm
              </View>
            </View>
            <SafeAreaBottom />
          </View>
        </View>
      </View>
    </MainLayout>
  );
};
export default CustomerSelect;

import Taro from "@tarojs/taro";
import { View } from "@tarojs/components";
import { observer } from "mobx-react";
import { MainLayout, FormInput, TextareaField } from "@/components";
import { Button as NutButton, Form, TextArea } from "@nutui/nutui-react-taro";
import { CustomerInfo } from "../../../mobx/model/UserInfo";
import { useState } from "react";
import { useStores, useI18n } from "@/hook";
import { toJump } from "@/utils";
import sty from "./index.module.scss";

const RegistrationPage = () => {
  const { customerStore } = useStores();
  const { t } = useI18n();
  const route: any = Taro.getCurrentInstance().router?.params;

  const [noteValue, setNoteValue] = useState("");
  const [isFormValid, setIsFormValid] = useState(false);

  const handleSubmit = async (formData: any) => {
    console.log("Registration data:", formData);

    // 准备注册数据
    const customerData: CustomerInfo = {
      mobile: formData.mobile,
      first_name: formData.firstName,
      last_name: formData.lastName,
      email: formData.email || "",
      node: noteValue, // 备注信息
    };

    // 调用 customerStore 的注册方法
    customerStore.registerCustomer(customerData, () => {
      toJump("pages/home/<USER>");
    });
  };

  const handleSubmitFailed = (_, errorFields: any) => {
    console.log("Form validation failed:", errorFields);
    Taro.showToast({
      title: "Please fill in required fields",
      icon: "error",
    });
  };

  return (
    <MainLayout headerType="withBack">
      <View className={["px-5 bg-white", sty.formArea].join(" ")}>
        <View className="text-md text-black mt-20 mb-8">{t("registration.title")}</View>

        <Form
          onFinish={handleSubmit}
          onFinishFailed={handleSubmitFailed}
          initialValues={{
            mobile: route.mobile || "",
            firstName: "",
            lastName: "",
            email: "",
          }}
          footer={
            <NutButton nativeType="submit" block type="default" className="btn-default mt-16 bg-brand-898989 color-brand-" formType="submit">
              {t("registration.signUp")}
            </NutButton>
          }>
          <View className="flex flex-col gap-6">
            {/* Phone Number - Required */}
            <FormInput
              name="mobile"
              label={t('registration.phoneNumber')}
              type="text"
							value={route.mobile}
              required
              showRequire={true}
              rules={{
                required: { required: true, message: t("registration.pleaseEnterValidMobile") },
              }}
            />

            {/* First Name and Last Name - Side by side */}
            <View className="flex">
              <FormInput
                name="firstName"
                class_name="mr-2"
                label={t("registration.firstName")}
                required
                showRequire={true}
                rules={{
                  required: { required: true, message: t("registration.pleaseEnterFirstName") },
                }}
              />
              <FormInput
                name="lastName"
                class_name="ml-2"
                label={t("registration.lastName")}
                required
                showRequire={true}
                rules={{
                  required: { required: true, message: t("registration.pleaseEnterLastName") },
                }}
              />
            </View>

            {/* Email */}
            <FormInput name="email" label={t("registration.email")} type="text" rules={{}} />

            {/* Note - 使用新的TextareaField组件 */}
            <TextareaField label={t("registration.note")} value={noteValue} onChange={setNoteValue} placeholder="Comments" maxLength={100} />
          </View>
        </Form>
      </View>
    </MainLayout>
  );
};

export default observer(RegistrationPage);

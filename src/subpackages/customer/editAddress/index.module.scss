.editAddressPage {
  @apply h-full flex flex-col;
  .content {
    @apply flex-1 px-8 pt-8;
    .theme {
      @apply text-base text-brand-2D2E2C leading-10;
    }
    :global {
      .nut-cell-group {
        .nut-cell-group-wrap {
          @apply my-0;
          .nut-cell {
            @apply p-0 pt-12;
            .nut-cell-title {
              @apply p-0 mr-0 w-auto pr-[10rpx] relative h-8 flex items-center  mb-6;
              .nut-form-item-labeltxt {
                @apply text-sm text-brand-2D2E2C leading-8 h-8;
              }
              .required {
                @apply right-0 left-auto top-0 mr-0 h-8;
              }
            }
            .nut-cell-value {
              .nut-form-item-body-slots {
                @apply pb-6 border-b-1-xs;

              }
              .nut-form-item-body-tips {
                @apply text-xs leading-8 text-brand-C33333;

              }
              .h5-input {
                @apply text-sm leading-8 text-brand-2D2E2C ;
              }
            }
          }
        }
      }
    }

    .row {
      @apply mt-12 ;
      .rowTitle {
        @apply flex text-sm leading-8  text-brand-2D2E2C mb-6;
        .star {
          @apply text-brand-C33333 ml-1 text-xl;
        }
      }
      .inputArea {
        @apply  relative ;
        .prefix {
          @apply h-8  text-sm leading-8  text-brand-2D2E2C absolute flex items-center;
          z-index: 10;
          .line {
            @apply w-1 h-5 bg-brand-E5E5E5 ml-6;
          }
        }


        :global {
          .nut-cell {
            @apply py-0 border-0;
            .nut-form-item-label {
              display: none !important;
            }

            .nut-form-item-body-slots {
              @apply border-none;
            }
            .h5-input {
              @apply h-8 leading-8  flex items-center text-sm leading-8 text-brand-2D2E2C pl-23;
              line-height: 16px !important;
              height: auto !important;
            }
          }
        }
      }
    }

   
    .addressInfo {
      @apply text-sm leading-8  text-brand-2D2E2C ;
    }
    .noAddress {
      @apply text-brand-898989;
    }
  }

  .btnArea {
    @apply px-8 pt-8 flex justify-between items-center;
    .btn {
      @apply flex-1 h-22 flex justify-center items-center;
      &:first-child {
        @apply mr-6 border-1-area;
      }
      &:last-child {
        @apply btn-default;
      }
    }
  }

  .addressArea {
    .topRow {
      @apply flex justify-between items-center;
    }
    .pickerView {
      @apply h-112;
      .pickerViewColumn {
        // background-color: pink;
        :global {
          .h5-view {
            // @apply h-22 flex justify-center items-center;
          }
        }
      }
      .selectedPicker {
        @apply h-22;
      }
    }

    .operateArea {
      @apply pt-6 px-8 border-t-1-xs;
      .btn {
        @apply btn-black text-base flex justify-center items-center w-full box-border;
      }
    }
  }
}

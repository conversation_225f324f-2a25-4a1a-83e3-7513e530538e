import { View, Input, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>iewColumn } from "@tarojs/components";
import Taro from "@tarojs/taro";
import { MainLayout } from "@/components";
import sty from "./index.module.scss";
import {
  Popup,
  Form,
  Picker,
  Cell,
  Button,
  type FormInstance
} from "@nutui/nutui-react-taro";
import { ArrowRight } from "@nutui/icons-react-taro";
import IconFont from "@/components/iconfont";
import { SafeAreaBottom } from "@/components";

interface PickerOption {
  text: string | number;
  value: string | number;
  disabled?: boolean;
  children?: PickerOption[];
  className?: string | number;
}
import { Children, useState } from "react";

const EditAddress = () => {
  const [form] = Form.useForm();
  const [asyncDesc, setasyncDesc] = useState("");

  const pickerOptions = [
    { value: 4, text: "BeiJing" },
    { value: 1, text: "NanJ<PERSON>" },
    { value: 2, text: "WuXi" },
    { value: 8, text: "DaQing" },
    { value: 9, text: "SuiHua" },
    { value: 10, text: "WeiFang" },
    { value: 12, text: "Shi<PERSON>iaZhuang" }
  ];

  const [address, setAddress] = useState("");
  const [selectProvince, setSelectProvince] = useState({});
  const [selectCity, setSelectCity] = useState({});
  const [selectArea, setSelectArea] = useState({});
  const [showBasic, setShowBasic] = useState(false);

  const [asyncData, setAsyncData] = useState([
    {
      value: 1,
      text: "北京市",
      children: [
        {
          value: 1,
          text: "朝阳区",
          children: [
            {
              value: 1,
              text: "朝阳区1",
              children: []
            }
          ]
        },
        { value: 2, text: "海淀区" },
        { value: 3, text: "大兴区" },
        { value: 4, text: "东城区" },
        { value: 5, text: "西城区" },
        { value: 6, text: "丰台区" }
      ]
    },
    {
      value: 2,
      text: "上海市",
      children: [
        {
          value: 21,
          text: "上海市21",
          children: [
            {
              value: 211,
              text: "朝阳区211",
              children: []
            }
          ]
        },
        { value: 22, text: "上海市22" },
        { value: 23, text: "上海市23" },
        { value: 24, text: "上海市24" },
        { value: 25, text: "上海市25" },
        { value: 26, text: "上海市26" }
      ]
    }
  ]);

  const updateChooseValueCustmer = (
    options: PickerOption[],
    values: (string | number)[],
    columnIndex: number
  ) => {
    console.log("updateChooseValueCustmer", values, options);
    if (columnIndex === 0 && values[0] === 2) {
      setTimeout(() => {
        if (asyncData[1].children.length === 0) {
          asyncData[1].children = [
            {
              value: 1,
              text: "黄埔区"
            },
            {
              value: 2,
              text: "长宁区"
            },
            {
              value: 3,
              text: "普陀区"
            },
            {
              value: 4,
              text: "杨浦区"
            },
            {
              value: 5,
              text: "浦东新区"
            }
          ];
          setAsyncData([...asyncData]);
        }
      }, 100);
    }
  };

  const submitFailed = (error: any) => {
    console.log('------error',error);
    
    // Taro.showToast({ title: JSON.stringify(error), icon: "error" });
  };

  const submitSucceed = (values: any) => {
    console.log("-----values", values);

    Taro.showToast({ title: JSON.stringify(values), icon: "success" });
  };

  const onAddressPicker = (option, value) => {
    setAddress(option[0]?.text + "-" + option[1]?.text + "-" + option[2]?.text);

    return value;
  };

  const province = [
    {
      value: 1,
      text: "上海市"
    },
    {
      value: 2,
      text: "北京市"
    }
  ];
  const city = [
    {
      value: 1,
      text: "闵行区"
    },
    {
      value: 2,
      text: "浦东新区"
    }
  ];
  const area = [
    {
      value: 1,
      text: "浦锦街道"
    },
    {
      value: 2,
      text: "春申街道"
    }
  ];

  const onColumnChange = (value, type) => {
    console.log("-----change", value);

    if (type == "province") {
      setSelectProvince(value);
    } else if (type == "city") {
      setSelectCity(value);
    } else {
      setSelectArea(value);
    }
  };

  const onColumnSelected = value => {
    console.log("-----value", value);
  };

  const onAddressConfirm = () => {
    const provinceV = selectProvince?.text ?? province[0].text;
    const cityV = selectCity?.text ?? city[0].text;
    const areaV = selectArea?.text ?? area[0].text;
    const provinceId = selectProvince?.text ?? province[0].value;
    const cityId = selectCity?.text ?? city[0].value;
    const areaId = selectArea?.text ?? area[0].value;

    const res = provinceV + "-" + cityV + "-" + areaV

    setAddress(res);
    setShowBasic(false);
    form.setFieldValue("address", res);
    // form.resetFields()

  };

  return (
    <MainLayout
      initOptions={{
        inited: false,
        initLoading: false,
        loadingFullScreen: true
      }}
      // 使用便捷配置方式
      headerType="withBack"
      headerBackgroundColor="#fff"
      style={{
        backgroundSize: "100% auto",
        backgroundColor: "#fff"
      }}
    >
      <View className={sty.editAddressPage}>
        <View className={sty.content}>
          <View className={sty.theme}>Switch Delivery Address</View>
          <Form
            labelPosition="top"
            starPosition="right"
            initialValues={{}}
            form={form}
            onFinish={values => submitSucceed(values)}
            onFinishFailed={(values, errors) => submitFailed(errors)}
              validateTrigger={'onBlur'}

          >
            <Form.Item
              label="字段A"
              name="username"
              rules={[{ required: true }]}
              getValueFromEvent={(...args) => {
                return args[0].detail.value;
              }}
            >
              <Input placeholder="Please enter" type="text" />
            </Form.Item>
            <Form.Item shouldUpdate noStyle>
              {({ getFieldValue }: FormInstance) => {
                return (
                  <View className={sty.row}>
                    <View className={sty.rowTitle}>
                      <View className={sty.title}>Phone Number</View>
                      <View className={sty.star}>*</View>
                    </View>
                    <View className={sty.inputArea}>
                      <View className={sty.prefix}>
                        +86
                        <View className={sty.line}></View>
                      </View>

                      <Form.Item
                        label=" "
                        name="age"
                        rules={[{ required: true }]}
                        getValueFromEvent={(...args) => {
                          return args[0].detail.value;
                        }}
                      >
                        <Input
                          placeholder="Please enter"
                          type="number"
                          selection-start="0"
                          selection-end="0"
                        />
                      </Form.Item>
                    </View>
                  </View>
                );
              }}
            </Form.Item>

            <Form.Item
              label="Address"
              name="address"
              trigger="onChange"
              onClick={(event, ref: any) => {
                setShowBasic(true);
              }}
              rules={[{ required: true }]}
            >
              <Input placeholder="Please enter" type="text" disabled />
            </Form.Item>

            <Form.Item
              label="Detailed Address"
              name="detail"
              rules={[{ required: true }]}
              getValueFromEvent={(...args) => {
                return args[0].detail.value;
              }}
            >
              <Input placeholder="Please enter" type="text" />
            </Form.Item>
          </Form>
        </View>
        <View className={sty.btnArea}>
          <View className={sty.btn}>Cancel</View>
          <View className={sty.btn} onClick={() => form.submit()}>
            Confirm
          </View>
        </View>

        <Popup
          visible={showBasic}
          onClose={() => {
            setShowBasic(false);
          }}
          position="bottom"
        >
          <View className={sty.addressArea}>
            <View className={sty.content}>
              <View className={sty.topRow}>
                <View>Address</View>
                <View>
                  <IconFont name="close" size={24} />
                </View>
              </View>
              <PickerView
                indicatorClass={sty.selectedPicker}
                className={sty.pickerView}
                value={[1, 1, 1]}
                onChange={onColumnSelected}
              >
                <PickerViewColumn className={sty.pickerViewColumn}>
                  {province.map(item => {
                    return (
                      <View
                        onClick={() => onColumnChange(item, "province")}
                        className={sty.pickerViewColumnContent}
                      >
                        {item.text}年
                      </View>
                    );
                  })}
                </PickerViewColumn>
                <PickerViewColumn className={sty.pickerViewColumn}>
                  {city.map(item => {
                    return (
                      <View
                        className={sty.pickerViewColumnContent}
                        onClick={() => onColumnChange(item, "city")}
                      >
                        {item.text}月
                      </View>
                    );
                  })}
                </PickerViewColumn>
                <PickerViewColumn className={sty.pickerViewColumn}>
                  {area.map(item => {
                    return (
                      <View
                        className={sty.pickerViewColumnContent}
                        onClick={() => onColumnChange(item, "area")}
                      >
                        {item.text}日
                      </View>
                    );
                  })}
                </PickerViewColumn>
              </PickerView>
            </View>
            <View className={sty.operateArea}>
              <View className={sty.btn} onClick={onAddressConfirm}>
                Confirm
              </View>
              <SafeAreaBottom />
            </View>
          </View>
        </Popup>
      </View>
    </MainLayout>
  );
};

export default EditAddress;

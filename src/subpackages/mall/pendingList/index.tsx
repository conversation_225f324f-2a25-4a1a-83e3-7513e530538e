import { View } from "@tarojs/components";
import { MainLayout } from "@/components";
import sty from "./index.module.scss";
import IconFont from "@/components/iconfont";
import GoodCard from "../components/goodCard";
import AlterationPopup from "../components/alterationPopup";
import { useState } from "react";

const PendingList = () => {
  return (
    <MainLayout
      initOptions={{
        inited: true,
        initLoading: true,
        loadingFullScreen: true
      }}
      headerType="withBack"
      showBottomPlaceholder={true}
      headBarConfig={{
        backgroundColor: "#EFEFEF"
      }}
    >
    </MainLayout>
  );
};

export default PendingList;

import { View, Button } from "@tarojs/components";
import { useState, useEffect } from "react";
import Taro from "@tarojs/taro";
import IconFont from "@/components/iconfont";
import { Popup } from "@/components";

// 定义店铺库存数据类型
interface StoreInventoryItem {
  code: number;
  name: string;
  address: string;
  mobile: string;
  store_stock: number;
}

interface StoreInventoryPopupProps {
  visible: boolean;
  onClose: () => void;
  barcode?: string;
}

const StoreInventoryPopup = ({
  visible,
  onClose,
  barcode,
}: StoreInventoryPopupProps) => {
  const [storeList, setStoreList] = useState<StoreInventoryItem[]>([
    {
      code: 1,
      name: "Shanghai Anfu Store",
      address: "No. 500 Dongyu Road, Pudong New District, Shanghai",
      mobile: "021-12345678",
      store_stock: 10,
    },
    {
      code: 2,
      name: "Shanghai Anfu Store",
      address: "No. 500 Dongyu Road, Pudong New District, Shanghai",
      mobile: "010-87654321",
      store_stock: 0,
    },
    {
      code: 3,
      name: "Shanghai Anfu Store",
      address: "No. 500 Dongyu Road, Pudong New District, Shanghai",
      mobile: "020-11223344",
      store_stock: 8,
    },
    {
      code: 3,
      name: "SUITSUPPLY广州店",
      address: "广州市天河区天河路208号",
      mobile: "020-11223344",
      store_stock: 8,
    },
  ]);

  // 获取店铺库存数据
  const fetchStoreInventory = async () => {
    if (!barcode) return;

    try {
      const res = await Taro.request({
        url: "/web/mobile/default.php?c=goods&m=store_stock",
        method: "GET",
        data: { barcode },
      });

      if (res.data.code === 0) {
        setStoreList(res.data.data);
      }
    } catch (error) {
      console.error("获取店铺库存失败:", error);
      // 使用mock数据作为fallback
    } finally {
    }
  };

  // 打电话
  const makePhoneCall = (phoneNumber: string) => {
    Taro.makePhoneCall({
      phoneNumber: phoneNumber,
    }).catch((error) => {
      console.error("拨打电话失败:", error);
    });
  };

  useEffect(() => {
    if (visible && barcode) {
      fetchStoreInventory();
    }
  }, [visible, barcode]);

  return (
    <Popup
      visible={visible}
      onClose={onClose}
      title="Nearby Store Inventory"
      height={530}
    >
      {storeList.length > 0 ? (
        storeList.map((store, index) => (
          <View key={store.code} className="py-12  leading-10 border-b-1-ios">
            {/* 店铺名称 */}
            <View className="text-base font-medium  font-gt-america-medium text-brand-2D2E2C mb-3">
              {store.name}
            </View>

            {/* 地址 */}
            <View className="flex items-start mb-4 leading-8">
              <View className="mr-2">
                <IconFont name="address" size={16} color="#898989" />
              </View>
              <View className="text-sm text-brand-898989 flex-1">
                {store.address}
              </View>
            </View>

            {/* 电话 */}
            <View className="flex items-center mb-10 leading-8">
              <View className="mr-2">
                <IconFont name="call" size={16} color="#898989" />
              </View>
              <View
                className="text-sm text-brand-898989 underline"
                onClick={() => makePhoneCall(store.mobile)}
              >
                {store.mobile}
              </View>
            </View>

            {/* 库存 */}
            <View className="text-sm text-brand-2D2E2C leading-8">
              In-store stock:
              <View
                className={[
                  "bg-brand-000000 text-white rounded-50 text-sm inline-block leading-8 h-8 px-2 text-center ml-2",
                  store.store_stock == 0 && "bg-brand-BABABA",
                ].join(" ")}
              >
                {store.store_stock}
              </View>
            </View>
          </View>
        ))
      ) : (
        <View className="flex justify-center items-center py-8 text-sm leading-8">
          <View className="text-brand-898989">No Nearby Store Inventory.</View>
        </View>
      )}
    </Popup>
  );
};

export default StoreInventoryPopup;

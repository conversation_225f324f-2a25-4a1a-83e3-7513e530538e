.orderDetailPage {
  @apply h-full flex flex-col;
  .mainContent {
    @apply mt-8 mx-8 flex-1 overflow-scroll;
    .orderStatus {
      @apply pt-8 px-8 pb-10 border-1-area;
      .statusIcon {
        @apply flex justify-center items-center;
      }
      .statusText {
        @apply mt-4 text-base leading-10 text-black text-center;
      }
      .price {
        @apply mt-8 text-xl leading-12 text-brand-2D2E2C text-center;
      }
      .totalAmount {
        @apply mt-2 text-sm leading-8 text-brand-898989 text-center;
      }
    }
    .inStoreInfo {
      @apply mt-6 py-10 px-8 border-1-area;
      .storeTitle {
        @apply text-base leading-10 font-medium text-brand-2D2E2C;
      }
      .storePersonInfo {
        @apply mt-8 flex  items-center  text-sm leading-8 text-brand-2D2E2C;
        .name {
          @apply mr-8;
        }
      }
      .storeAddress {
        @apply mt-2 text-sm leading-8 text-brand-898989;
      }
    }

    .customerInfo {
      @apply p-8 border-1-area mt-8;
      .orderNumber,
      .salesAssociate,
      .customer,
      .orderTime {
        @apply flex justify-between items-center text-sm leading-8 text-brand-2D2E2C mb-8;
        .rightInfo {
          @apply font-medium flex items-center;
          .copyIcon {
            @apply ml-2;
          }
        }
      }
      .customer {
        .leftInfo {
          @apply self-start;
        }
        .rightInfo {
          @apply block;
          .name,
          .mobile,
          .email {
            @apply text-right;
          }

          .mobile,
          .email {
            @apply mt-2;
          }
        }
      }
      .orderTime {
        @apply mb-0;
      }
    }
    .goodDetail {
      @apply p-8 border-1-area mt-8;
      .firstTitle {
        @apply text-md leading-10 text-brand-2D2E2C font-medium;
      }
      .inStore {
        @apply mt-8;
        .inStoreTitle {
          @apply text-base leading-10 text-brand-2D2E2C;
        }
        .clothings {
          @apply pt-6 pb-10 border-b-1;
          .cardRow {
            @apply mb-8;
            &:last-child {
              @apply mb-0;
            }
          }
        }
      }
      .onLine {
        .onLineTitle {
          @apply mt-10;
        }
        .clothings {
          @apply pt-6 pb-10 border-b-1;
          .cardRow {
            @apply mb-8;
            &:last-child {
              @apply mb-0;
            }
          }
        }
      }

      .priceInfo {
        @apply pt-11 pb-10 border-b-1;
        .row {
          @apply flex justify-between items-center;
          &:last-child {
            @apply mt-9;
          }
          .leftTitle {
            @apply text-sm leading-8 text-brand-898989;
          }
          .rightTitle {
            @apply text-base leading-10 text-brand-2D2E2C font-medium;
          }
        }
      }
      .totalMount {
        @apply flex justify-between items-center leading-10 text-brand-2D2E2C font-medium mt-10;
        .totalPrice {
          @apply text-base;
        }
        .totalPriceV {
          @apply text-md;
        }
      }
    }
    .orderInformation {
      @apply p-8 px-6 border-1-area mt-8;
      .orderInformationTitle {
        @apply text-base leading-10 font-medium text-brand-2D2E2C;
      }
      .row {
        @apply flex justify-between items-center text-sm leading-8 mt-10;
        .title {
          @apply text-brand-898989;
        }
        .value {
          @apply text-brand-2D2E2C;
        }
      }
    }
  }
  .btnOperate {
    @apply pt-6 px-8 flex justify-between items-center shadow bg-white mt-8;
    .btn {
      @apply flex-1 flex justify-center items-center border-1-area h-22;
      &:last-child {
        @apply btn-default ml-6;
      }
    }
  }
}

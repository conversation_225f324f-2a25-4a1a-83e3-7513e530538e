import { View } from "@tarojs/components";
import { MainLayout } from "@/components";
import sty from "./index.module.scss";
import IconFont from "@/components/iconfont";
import GoodCard from "../components/goodCard";
import AlterationPopup from "../components/alterationPopup";
import { useState } from "react";

const OrderDetail = () => {
  const [showAlteration,setShowAlteration] = useState(false)
  const onQueryAlteration = () => {
    setShowAlteration(true)
  }

  const onCloseAlteration = () => {
    setShowAlteration(false)
  }

  const onConfirmAlteration = () => {
    setShowAlteration(false)

  }
  return (
    <MainLayout
      initOptions={{
        inited: true,
        initLoading: true,
        loadingFullScreen: true
      }}
      headerType="withBack"
      showBottomPlaceholder={true}
      headBarConfig={{
        backgroundColor: "#EFEFEF"
      }}
    
    >
      <View className={sty.orderDetailPage}>
        <View className={sty.mainContent}>
          <View className={sty.orderStatus}>
            <View className={sty.statusIcon}>
              <IconFont name="Successful" size={32} />
            </View>
            <View className={sty.statusText}>Order Completed</View>
            <View className={sty.price}>¥6360</View>
            <View className={sty.totalAmount}>Total Amount</View>
          </View>

          <View className={sty.inStoreInfo}>
            <View className={sty.storeTitle}>In-Store Pickup</View>
            <View className={sty.storePersonInfo}>
              <View className={sty.name}>CHEN.MR </View>
              <View className={sty.mobile}>150009411848</View>
            </View>
            <View className={sty.storeAddress}>
              XuHui District,Shanghai 169 Anfu Road XuHui District
            </View>
          </View>

          <View className={sty.customerInfo}>
            <View className={sty.orderNumber}>
              <View className={sty.leftInfo}>Order Number </View>
              <View className={sty.rightInfo}>
                7ab0501e
                <View className={sty.copyIcon}>
                  <IconFont name="content_copy" size={16} />
                </View>
              </View>
            </View>

            <View className={sty.salesAssociate}>
              <View className={sty.leftInfo}>Sales Associate </View>
              <View className={sty.rightInfo}>May</View>
            </View>

            <View className={sty.customer}>
              <View className={sty.leftInfo}>Customer </View>
              <View className={sty.rightInfo}>
                <View className={sty.name}>Liu, Chen</View>
                <View className={sty.mobile}>15000941949</View>
                <View className={sty.email}><EMAIL></View>
              </View>
            </View>

            <View className={sty.orderTime}>
              <View className={sty.leftInfo}>Order time</View>
              <View className={sty.rightInfo}>2025-04-22 09:23:58</View>
            </View>
          </View>

          <View className={sty.goodDetail}>
            <View className={sty.firstTitle}>Item Details</View>

            <View className={sty.inStore}>
              <View className={sty.inStoreTitle}>In-Store</View>
              <View className={sty.clothings}>
                <View className={sty.cardRow}>
                  <GoodCard cropping={true} edit={false} onAlteration={onQueryAlteration}/>
                </View>
                <View className={sty.cardRow}>
                  <GoodCard cropping={false} edit={false} />
                </View>
              </View>
            </View>

            <View className={sty.onLine}>
              <View className={sty.onLineTitle}>Online</View>
              <View className={sty.clothings}>
                <View className={sty.cardRow}>
                  <GoodCard cropping={true} edit={false}  onAlteration={onQueryAlteration}/>
                </View>
                <View className={sty.cardRow}>
                  <GoodCard cropping={false} edit={false}  />
                </View>
              </View>
            </View>

            <View className={sty.priceInfo}>
              <View className={sty.row}>
                <View className={sty.leftTitle}>Original Price</View>
                <View className={sty.rightTitle}>¥6360</View>
              </View>

              <View className={sty.row}>
                <View className={sty.leftTitle}>Total Discount Amount</View>
                <View className={sty.rightTitle}>-¥0</View>
              </View>
            </View>

            <View className={sty.totalMount}>
              <View className={sty.totalPrice}>Total Price</View>
              <View className={sty.totalPriceV}>¥6360</View>
            </View>
          </View>

          <View className={sty.orderInformation}>
            <View className={sty.orderInformationTitle}>Order Information</View>
            <View className={sty.row}>
              <View className={sty.title}>Receipt Number</View>
              <View className={sty.value}>4115</View>
            </View>
            <View className={sty.row}>
              <View className={sty.title}>Service Store</View>
              <View className={sty.value}>Shanghai Anfu Store</View>
            </View>

            <View className={sty.row}>
              <View className={sty.title}>Order Channel</View>
              <View className={sty.value}>Physical store sales</View>
            </View>
            <View className={sty.row}>
              <View className={sty.title}>Purchase Intent</View>
              <View className={sty.value}>Casual</View>
            </View>
          </View>
        </View>

        <View className={sty.btnOperate}>
          <View className={sty.btn}>Start Return</View>
          <View className={sty.btn}>Print Receipt</View>
        </View>

        <AlterationPopup visible={showAlteration} onClose = {onCloseAlteration} onConfirm={onConfirmAlteration}/>
      </View>
    </MainLayout>
  );
};

export default OrderDetail;

.scrollViewArea {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  gap: 7px;
  width: 100%;
}
.goodArea {
  @apply flex-1 mt-8 overflow-scroll inline-block;

  .good {
    @apply mb-10;
    width: calc(50vw - 3.5px - 16px);

    .img {
      @apply w-full h-111 bg-brand-898989;
    }
    .goodContent {
      @apply px-4 pt-4;
      .title {
        @apply text-base leading-10 text-brand-2D2E2C font-bold;
      }
      .des {
        @apply text-sm leading-8 text-brand-898989 mt-1;
      }
      .price {
        @apply text-sm leading-8 text-brand-2D2E2C mt-4;
      }
    }
  }
}

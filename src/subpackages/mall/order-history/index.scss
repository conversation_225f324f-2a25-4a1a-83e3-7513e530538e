.order-history-page {
  .search-container {
    position: sticky;
    top: 0;
    z-index: 50;
    background: white;
    border-bottom: 1px solid #e5e7eb;
    padding-top: 32px;
    
    .search-wrapper {
      display: flex;
      align-items: center;
      gap: 12px;
      padding: 0 16px;
    }
  }

  .order-list-container {
    flex: 1;
    background: #f9fafb;
    
    .order-list-scroll {
      height: calc(100vh - 120px);
    }
    
    .order-list-content {
      padding: 16px;
    }
  }

  .order-item {
    background: white;
    border-radius: 8px;
    border: 1px solid #e5e7eb;
    margin-bottom: 16px;
    padding: 16px;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);

    .order-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 12px;

      .customer-info {
        .customer-name {
          font-size: 18px;
          font-weight: 500;
          color: #111827;
          margin-bottom: 4px;
        }

        .customer-phone {
          font-size: 14px;
          color: #6b7280;
        }
      }

      .order-time {
        text-align: right;
        
        .time-text {
          font-size: 14px;
          color: #6b7280;
          margin-bottom: 4px;
        }
      }
    }

    .product-images {
      margin-bottom: 16px;

      .product-scroll {
        white-space: nowrap;
      }

      .product-list {
        display: flex;
        gap: 8px;
      }

      .product-item {
        flex-shrink: 0;

        .product-image {
          width: 96px;
          height: 128px;
          border-radius: 8px;
          background: #f3f4f6;
        }
      }
    }

    .order-footer {
      display: flex;
      justify-content: space-between;
      align-items: flex-end;

      .order-summary {
        .quantity {
          font-size: 14px;
          color: #6b7280;
          margin-bottom: 4px;
        }

        .total-amount {
          font-size: 20px;
          font-weight: bold;
          color: #111827;
        }
      }

      .retrieve-button {
        background: #374151;
        color: white;
        padding: 12px 24px;
        border-radius: 8px;
        font-size: 14px;
        font-weight: 500;
        cursor: pointer;
        transition: background-color 0.2s;

        &:hover {
          background: #1f2937;
        }

        &:active {
          background: #111827;
        }
      }
    }
  }

  .empty-state {
    text-align: center;
    color: #6b7280;
    margin-top: 80px;
    font-size: 16px;
  }

  .loading-state {
    text-align: center;
    color: #6b7280;
    margin-top: 80px;
    font-size: 16px;
  }
}

// 响应式适配
@media (max-width: 375px) {
  .order-history-page {
    .order-item {
      .order-header {
        .customer-info {
          .customer-name {
            font-size: 16px;
          }
        }
      }

      .order-footer {
        .order-summary {
          .total-amount {
            font-size: 18px;
          }
        }

        .retrieve-button {
          padding: 10px 20px;
          font-size: 13px;
        }
      }
    }
  }
}

import { View } from "@tarojs/components";
import { Cell, Dialog, Image } from "@nutui/nutui-react-taro";
import React, { useState } from "react";
import sty from "./index.module.scss";
import IconFont from "@/components/iconfont";
interface GoodType {
  goods_img:string;
  goods_sn:string;
  goods_name:string;
  goods_price:number;
  discription:string;
  color:string;
  size:string;
  serial_no?:string
  barcode?:string

}
interface GoodModalType {
  visible:boolean
  title?: string;
  confirmText: string;
  cancelText: string;
  onConfirm: () => void;
  onCancel: () => void;
  info: GoodType
}

const GoodModal:React.FC<GoodModalType> = (props) => {
  const {title,visible,confirmText,cancelText,onConfirm,onCancel,info} = props
  return (
    <Dialog
      className={sty.goodModal}
      visible={visible}
      onConfirm={onConfirm}
      onCancel={onCancel}
      confirmText={confirmText}
      cancelText={cancelText}
    >
      <View className={sty.mainContent}>
        <View className={sty.closeIcon}>
          <IconFont name="close" size={24} />
        </View>

        <View className={sty.title}>{title??'Product Scanned'}</View>
        <View className={sty.content}>
          <View className={sty.clothImg}>
            <Image src={info.goods_img} />
          </View>
          <View className={sty.info}>
            <View className={sty.serialNumber}>{info.goods_sn}</View>
            <View className={sty.goodName}>{info.goods_name}</View>

            <View className={sty.skuInfo}>
              <View className={sty.color}>Color: {info.color}</View>
              <View className={sty.size}>Size: {info.size}</View>
            </View>

            <View className={sty.price}>¥{info.goods_price}</View>
          </View>
        </View>
      </View>
    </Dialog>
  );
};

export default GoodModal;

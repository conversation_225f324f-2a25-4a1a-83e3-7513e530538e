import { View } from "@tarojs/components";
import sty from "./index.module.scss";
import { Image } from "@nutui/nutui-react-taro";
import IconFont from "@/components/iconfont";
import Taro from "@tarojs/taro";
interface GoodCardType {
  cropping: boolean;
  edit: boolean;
  onAlteration?: () => void 
}

const GoodCard: React.FC<GoodCardType> = props => {
  const { cropping = false, edit = false,onAlteration } = props;
  return (
    <View className={sty.goodCard}>
      <View className={sty.goodContent}>
        <View className={sty.clothingImg}>
          <Image />
        </View>
        <View className={sty.clothingInfo}>
          <View className={sty.topInfo}>
            <View className={sty.identifier}>
              <View className={sty.identifierName}>D67120W12</View>
              <View
                className={sty.editPrice}
                style={{ display: edit ? "flex" : "none" }}
              >
                <IconFont name="Icon1" size={12} />
                ¥5180
              </View>
            </View>
            <View className={[sty.clothingName,edit ? sty.editMode : sty.normalMode ].join(' ')}  >
              Roma Light Gray Check Slouchy- Body Blazer
            </View>
          </View>

          <View
            className={[sty.bottomInfo,edit ? sty.editMode : sty.normalMode].join(' ')}
          >
            <View className={sty.sizeColor}>
              <View className={sty.color}>Color: Light Grey</View>
              <View className={sty.size}>SIZE: 54</View>
            </View>
            <View
              className={[sty.priceRow,cropping ? sty.croppingMode : sty.normalMode].join(' ')}
              
            >
              <View className={sty.priceV}>¥5180</View>
              <View
                className={sty.priceDetail}
                style={{ display: cropping ? "flex" : "none" }}
                onClick={onAlteration}
              >
                <View className={sty.editIcon}>
                  <IconFont name="Icon1" size={16} />
                </View>
                Show Details
              </View>
            </View>
          </View>
        </View>
      </View>
      <View className={sty.croppingContent} style={{display:cropping?'flex':"none"}}>
        <View className={sty.typeLeft}>
          <View className={sty.type}>Alteration </View>
          <View className={sty.typePrice}>¥820 </View>
        </View>
        <View className={sty.editTypePrice} >
          <View className={sty.editIcon}>
            <IconFont name="Icon1" size={12} />
          </View>
          ¥820
        </View>
      </View>
    </View>
  );
};
export default GoodCard;

.goodCard {
  .goodContent {
    @apply flex items-center justify-between;
    .clothingImg {
      @apply w-50 h-67;
    }
    .clothingInfo {
      @apply flex-1 flex flex-col justify-between ml-6 h-67;
      .topInfo {
        .identifier {
          @apply text-sm leading-8 text-brand-2D2E2C flex justify-between items-center;
          .editPrice {
            @apply ml-1 flex justify-center items-center border-1-area h-12 px-3 box-border;
          }
        }
        .clothingName {
          @apply mt-2 text-sm leading-8 text-brand-898989;
          word-break: break-all;
          white-space: normal;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 2;
          overflow: hidden;
          &.editMode {
            @apply mt-2;
          }
          &.normalMode {
            @apply mt-4;
          }
        }
      }
      .bottomInfo {
        @apply flex flex-col justify-between;
        &.editMode {
          @apply mt-5;
        }
        &.normalMode {
          @apply mt-6;
        }

        .sizeColor {
          @apply text-sm leading-8 text-brand-2D2E2C;
          .size {
            @apply mt-2;
          }
        }
        .priceRow {
          @apply flex  justify-between;
          &.croppingMode {
            @apply mt-4;
          }
          &.normalMode {
            @apply mt-5;
          }

          .priceV {
            @apply text-base leading-10 text-brand-2D2E2C font-medium self-end;
          }
          .priceDetail {
            @apply text-sm leading-8 text-brand-2D2E2C flex items-center px-2 h-14 border-1-area box-border;
            .editIcon {
              @apply mr-2;
            }
          }
        }
      }
    }
  }
  .croppingContent {
    @apply mt-2 flex items-center justify-between p-6 border-1-area-xs bg-brand-FAFAFA;
    .typeLeft {
      @apply flex items-center  text-base text-brand-2D2E2C;
      .type {
        @apply leading-8;
      }
      .typePrice {
        @apply leading-10  font-medium ml-18;
      }
    }

    .editTypePrice {
      @apply flex items-center text-sm leading-8 text-brand-2D2E2C border-1-area h-12 px-3 box-border rounded-24;
      .editIcon {
        @apply mr-1;
      }
    }
  }
}

import { View } from "@tarojs/components";
import { Popup, Image } from "@nutui/nutui-react-taro";
import sty from "./index.module.scss";
import IconFont from "@/components/iconfont";
import GoodCard from "../goodCard";
import { SafeAreaBottom } from "@/components";

interface AlterationType {
  visible: boolean;
  onClose: () => void
  onConfirm: () => void
}
const AlterationPopup: React.FC<AlterationType> = props => {
  const { visible,onClose,onConfirm } = props;
  return (
    <View className={sty.alterationPopup}>
      <Popup visible={visible} position="bottom" onClose={() => {}} lockScroll>
        <View className={sty.mainContent}>
          <View className={sty.row}>
            <View className={sty.title}>Alteration</View>
            <View className={sty.closeIcon} onClick={onClose}>
              <IconFont name="close" size={24} />
            </View>
          </View>

          <View className={sty.clothingContent}>
            <GoodCard edit={false} cropping={false} />

            <View className={sty.cropping}>
              <View className={sty.croppingTitle}>Show Detalls</View>
              <View className={sty.propertyRow}>
                <View className={sty.propertyName}>Knits Side Seams</View>
                <View className={sty.propertyV}>3.0</View>
              </View>
              <View className={sty.propertyRow}>
                <View className={sty.propertyName}>Knits Length</View>
                <View className={sty.propertyV}>-3.0</View>
              </View>
            </View>
          </View>
        </View>
        <View className={sty.btnOperate}>
          <View className={sty.btn} onClick={onConfirm}> Delete</View>
          <SafeAreaBottom />
        </View>
      </Popup>
    </View>
  );
};
export default AlterationPopup;

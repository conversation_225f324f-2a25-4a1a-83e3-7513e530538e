.alterationPopup {
  .mainContent {
    @apply pt-8 px-8 py-12;
    .row {
      @apply flex justify-between items-center;
      .title {
        @apply text-md leading-10 text-black;
      }
      
    }
    .clothingContent {
      @apply mt-12;
      .cropping {
        @apply pt-8 px-6 border-1-area mt-10;
        .croppingTitle {
          @apply text-base leading-10 text-brand-2D2E2C mt-2;

        }
        .propertyRow {
          @apply flex justify-between items-center text-sm leading-8 py-8 border-b-1;
          &:last-child {
            @apply border-none;
          }
          .propertyName {
            @apply font-light text-brand-898989;
            
          }
          .propertyV {
            @apply font-medium text-brand-2D2E2C;
          }

        }


      }



    }
  }
  .btnOperate {
    @apply pt-6 px-8 border-t-1;
    .btn {
      @apply flex justify-center items-center btn-default;


    }


  }


}

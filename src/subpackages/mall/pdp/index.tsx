import React, { useState, useRef, useEffect } from "react";
import { View, Image, ScrollView } from "@tarojs/components";
import "./index.scss";
import { MainLayout } from "@/components";
import IconFont from "@/components/iconfont";
import { Collapse } from "@nutui/nutui-react-taro";
import { ArrowDown } from "@nutui/icons-react-taro";
import { toJump } from "@/utils";

interface ProductDetailProps {}

const ProductDetail: React.FC<ProductDetailProps> = () => {
  const [currentView, setCurrentView] = useState<"product" | "details">(
    "product"
  );
  const [isAnimating, setIsAnimating] = useState(false); // 是否在过渡动画
  const startY = useRef<number>(0);  // 开始位置
  const currentY = useRef<number>(0); //当前位置
  const isDragging = useRef<boolean>(false); // 是否在拖动
  const [collapseActiveName,setCollapseActiveName] = useState([])

  // 触摸开始
  const handleTouchStart = (e: any) => {
    const touch = e.touches[0];
    startY.current = touch.clientY;
    currentY.current = touch.clientY;
    isDragging.current = true;
  };

  // 触摸移动
  const handleTouchMove = (e: any) => {
    if (!isDragging.current || isAnimating) return;

    const touch = e.touches[0];
    currentY.current = touch.clientY;
    const deltaY = currentY.current - startY.current;

    // 防止页面滚动
    if (Math.abs(deltaY) > 10) {
      e.preventDefault();
    }
  };

  // 触摸结束
  const handleTouchEnd = (e: any) => {
    if (!isDragging.current || isAnimating) return;

    const deltaY = currentY.current - startY.current;
    const threshold = 30; // 降低触发阈值到30px

    if (Math.abs(deltaY) > threshold) {
      setIsAnimating(true);

      if (deltaY < 0 && currentView === "product") {
        // 向上滑动，从产品视图切换到详情视图
        setCurrentView("details");
      } else if (deltaY > 0 && currentView === "details") {
        // 向下滑动，从详情视图切换到产品视图
        setCurrentView("product");
      }
    }

    isDragging.current = false;

    // 动画完成后重置状态
    setTimeout(() => {
      setIsAnimating(false);
    }, 200);
  };

  const onCollapseChange = (activeName) => {
    setCollapseActiveName(activeName)
  }

  return (
    <MainLayout
      initOptions={{
        inited: true,
        initLoading: true,
        loadingFullScreen: true
      }}
      headerType="withBack"
      showBottomPlaceholder={true}
      headBarConfig={{
        backgroundColor: "#EFEFEF"
      }}
    >
      <View className="productDetailContainer">
        <View className="mainContent ">
          <View
            className="productViewContainer"
            style={{
              transform:
                currentView === "product"
                  ? "translateY(0)"
                  : "translateY(-100%)"
            }}
          >
            <View className="productView">
              <View className="productImageContainer">
                <Image
                  className="product-image"
                  src="https://via.placeholder.com/400x600/f5f5f5/666666?text=Product+Image"
                  mode="aspectFill"
                />
              </View>

              <View
                className="bottomInfo"
                onTouchStart={handleTouchStart}
                onTouchMove={handleTouchMove}
                onTouchEnd={handleTouchEnd}
              >
                <View className="productInfo">
                  <View className="productName">
                    Roma Light Gray Check Slouchy- Body Bla...
                  </View>
                  <View className="productMaterial">
                    Summer Linen Cotton Blend by Ferla, Italy
                  </View>
                  <View className="productPrice">¥5180</View>
                </View>
              </View>
            </View>
          </View>
          <View
            className="detailsViewContainer"
            style={{
              transform:
                currentView === "details" ? "translateY(0)" : "translateY(100%)"
            }}
          >
            <View
              className="detailsView"
              onTouchStart={handleTouchStart}
              onTouchMove={handleTouchMove}
              onTouchEnd={handleTouchEnd}
            >
              <View className="productOverview">
                <View className="productName">
                  Roma Light Gray Check Slouchy- Body Bla...
                </View>
                <View className="productMaterial">
                  Summer Linen Cotton Blend by Ferla, Italy
                </View>
                <View className="productPrice">¥5180</View>
              </View>
              <View className="info">
                <Collapse
                  accordion
                  expandIcon={<ArrowDown />}
                  activeName={collapseActiveName}
                  onChange={onCollapseChange}
                >
                  <Collapse.Item title="Details and care" name="1" expandIcon={collapseActiveName.includes('1')? <IconFont name="closeCatalog" size={24}/>:<IconFont name="openCatalog" size={24}/>}>
                    <View className="property">
                      <View className="propertyName">MasterProductId</View>
                      <View className="propertyValue">H7326A</View>
                    </View>

                    <View className="property">
                      <View className="propertyName">Material</View>
                      <View className="propertyValue">Egyptian Cotton</View>
                    </View>
                    <View className="property">
                      <View className="propertyName">Fabric Composition</View>
                      <View className="propertyValue">Cotton</View>
                    </View>

                    <View className="property">
                      <View className="propertyName">Color</View>
                      <View className="propertyValue">Light Gray</View>
                    </View>
                    <View className="property">
                      <View className="propertyName">Place of Origin</View>
                      <View className="propertyValue">
                        Albi,Italian Republic
                      </View>
                    </View>

                    <View className="property">
                      <View className="propertyName">Collar</View>
                      <View className="propertyValue">
                        Classic One-Piece Collar
                      </View>
                    </View>

                    <View className="property">
                      <View className="propertyName">Cuff</View>
                      <View className="propertyValue">Single-Layer Cuff</View>
                    </View>
                    <View className="property">
                      <View className="propertyName">CareInstructions</View>
                      <View className="propertyValue">Machine Wash Warm</View>
                    </View>
                  </Collapse.Item>

                  <Collapse.Item title="Details and care" name="2" expandIcon={collapseActiveName.includes('2')? <IconFont name="closeCatalog" size={24}/>:<IconFont name="openCatalog" size={24}/>} >
                    <View className="property">
                      <View className="propertyName">MasterProductId</View>
                      <View className="propertyValue">H7326A</View>
                    </View>

                    <View className="property">
                      <View className="propertyName">Material</View>
                      <View className="propertyValue">Egyptian Cotton</View>
                    </View>
                    <View className="property">
                      <View className="propertyName">Fabric Composition</View>
                      <View className="propertyValue">Cotton</View>
                    </View>

                    <View className="property">
                      <View className="propertyName">Color</View>
                      <View className="propertyValue">Light Gray</View>
                    </View>
                    <View className="property">
                      <View className="propertyName">Place of Origin</View>
                      <View className="propertyValue">
                        Albi,Italian Republic
                      </View>
                    </View>

                    <View className="property">
                      <View className="propertyName">Collar</View>
                      <View className="propertyValue">
                        Classic One-Piece Collar
                      </View>
                    </View>

                    <View className="property">
                      <View className="propertyName">Cuff</View>
                      <View className="propertyValue">Single-Layer Cuff</View>
                    </View>
                    <View className="property">
                      <View className="propertyName">CareInstructions</View>
                      <View className="propertyValue">Machine Wash Warm</View>
                    </View>
                  </Collapse.Item>
                </Collapse>
              </View>
            </View>
          </View>
        </View>

        <View className="bottomOperate">
          <View className="shopCart">
            <View className="cartIcon">
              <IconFont name="shoppingbag" size={32} />
              <View className="count">10</View>
            </View>
          </View>
          <View className="selectSize btn-default" onClick={() => toJump(`/subpackages/mall/size/index?goods_sn=${1}&color=${2}`)}>Select size</View>
        </View>
      </View>
    </MainLayout>
  );
};

export default ProductDetail;

.productDetailContainer{
  width: 100vw;
  height: 100%;
  display: flex;
  flex-direction: column;
  .mainContent {
    position: relative;
    background: #efefef;
    overflow: hidden;
    flex: 1;
    .productViewContainer,
    .detailsViewContainer {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      transition: transform 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    }

    // 产品视图样式
    .productView {
      position: relative;
      width: 100%;
      height: 100%;
      display: flex;
      flex-direction: column;

      .productImageContainer {
        position: relative;
        background: #f5f5f5;
        overflow: scroll;
        height: 538px;
        .product-image {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }

        // 添加一个微妙的指示器
        &::before {
          content: "";
          position: absolute;
          bottom: 8px;
          left: 50%;
          transform: translateX(-50%);
          width: 40px;
          height: 4px;
          background: pink;
          border-radius: 2px;
        }
      }

      .bottomInfo {
        flex: 1;
        width: 100%;
        overflow: hidden;
        box-sizing: border-box;
        background: #fff;
        padding: 12px 16px 16px;
        border-radius: 8px 8px 0 0;
        backdrop-filter: blur(24px);
        @apply border-t-1-xs;

        cursor: grab;

        &:active {
          cursor: grabbing;
        }

      

        .productInfo {
          margin-bottom: 20px;

          .productName {
            @apply text-md leading-10 text-brand-2D2E2C font-medium;
          }

          .productMaterial {
            @apply text-sm leading-8 text-brand-898989 font-light mt-4;
          }
          .productPrice {
            @apply text-base leading-10 text-brand-2D2E2C mt-8;
          }
        }

        .action-buttons {
          display: flex;
          align-items: center;
          gap: 16px;

          .cart-button {
            position: relative;
            width: 48px;
            height: 48px;
            border: 2px solid #e0e0e0;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #fff;

            .cart-icon {
              font-size: 20px;
            }

            .cart-badge {
              position: absolute;
              top: -4px;
              right: -4px;
              width: 20px;
              height: 20px;
              background: #333;
              color: #fff;
              border-radius: 50%;
              display: flex;
              align-items: center;
              justify-content: center;
              font-size: 12px;
              font-weight: 600;
            }
          }

          .select-size-button {
            flex: 1;
            height: 48px;
            background: #333;
            color: #fff;
            border-radius: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: background-color 0.2s;

            &:active {
              background: #555;
            }
          }
        }
      }
    }

    // 详情视图样式
    .detailsView {
      position: relative;
      width: 100%;
      height: 100%;
      display: flex;
      flex-direction: column;
      background: #fff;
      // overflow: hidden; // 防止内容溢出
      overflow: scroll;

      .productOverview {
        @apply m-8 pb-8 border-b-1;
        .productName {
          @apply text-md leading-10 text-brand-2D2E2C font-medium;
        }

        .productMaterial {
          @apply text-sm leading-8 text-brand-898989 font-light mt-4;
        }

        .productPrice {
          @apply text-base leading-10 text-brand-2D2E2C mt-8;
        }
      }

      .info {
        @apply px-8;
        .nut-collapse {
          .nut-collapse-item {
            .nut-collapse-item-header {
              @apply px-0 py-11 border-b-1;
              .nut-collapse-item-title {
                @apply text-base leading-10 text-brand-2D2E2C;
              }
              .nut-collapse-item-icon-box {
                @apply size-12;
                .nut-collapse-item-icon {
                  @apply left-0;
                }
              }
            }
            .nut-collapse-item-content {
              .nut-collapse-item-content-text {
                @apply px-0 py-10;
              }
            }
          }
        }

        .property {
          @apply h-8 text-sm leading-8 mb-8 flex justify-between items-center;
          .propertyName {
            @apply text-brand-898989;
          }
          .propertyValue {
            @apply text-brand-2D2E2C;
          }
        }
      }





    }
  }
  .bottomOperate {
    @apply flex justify-between items-center pt-6 px-6  w-full box-border bg-white;
    .shopCart {
      @apply p-3 relative size-16;
      .count {
        @apply absolute right-0 top-0 size-8 rounded-8 bg-black text-xs flex justify-center items-center text-white;
      }
    }
    .selectSize {
      @apply flex-1 flex justify-center items-center text-base leading-10 ml-8;
    }
  }
}


// 触摸反馈
.productDetailContainer{
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-tap-highlight-color: transparent;
}


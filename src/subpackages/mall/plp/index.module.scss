.plpPage {
  @apply flex flex-col h-full;
  .topArea {
    .searchArea {
      @apply box-border pt-8;
    }
    .firstLevel {
      @apply pl-8 mt-4 border-b-1;

      :global {
        .nut-tabs {
          .nut-tabs-titles {
            @apply bg-white;
            .nut-tabs-list {
              .nut-tabs-titles-item {
                @apply px-0 mx-10 text-base leading-22 h-22;
                &:first-child {
                  @apply ml-0;
                }
                min-width: auto;
                .nut-tabs-titles-item-text {
                  @apply text-brand-898989;
                }
                .nut-tabs-titles-item-line {
                  @apply bottom-0 w-full bg-brand-2D2E2C;
                }
              }
              .nut-tabs-titles-item-active {
                .nut-tabs-titles-item-text {
                  @apply text-brand-898989 text-brand-2D2E2C font-bold;
                }
              }
            }
          }
        }
      }
    }
    .secondLevel {
      @apply pl-8 mt-4;
      :global {
        .nut-tabs-titles {
          @apply h-16 bg-white;
          .nut-tabs-titles-item {
            @apply h-16 leading-16 px-3;
            &:first-child {
              @apply pl-0;
            }
            .nut-tabs-titles-item-text {
              @apply bg-brand-EFEFEF rounded-[16px] px-8 h-16 flex items-center box-border text-sm text-brand-898989 border-none;
            }
          }

          .nut-tabs-titles-item-active {
            .nut-tabs-titles-item-text {
              @apply text-brand-2D2E2C font-bold border-none;
            }
          }
        }
      }
    }
    .threeFilterArea {
      @apply mt-12 px-8 flex justify-between items-center relative;
      .storeStock {
        :global {
          .nut-checkbox-label {
            @apply text-sm leading-8 text-brand-2D2E2C ml-4;
          }
        }
      }
      .rightFilterArea {
        @apply flex items-center;
        .filter,
        .sortBy {
          @apply text-sm leading-8 text-brand-2D2E2C flex items-center h-16 px-6 border-1-area box-border;
        }
        .filter {
          @apply mr-4;
        }
      }

      .panel {
        position: absolute;
        width: 100%;
        height: calc(100vh - 209px - 94px);
        background-color: pink;
        background: rgba(0, 0, 0, 0.5);
        z-index: 100;
        left: 0;
        top: 32px;
        transition: max-height 0.3s ease, visibility 0s linear;
        :global {
          .nut-radiogroup {
            @apply px-8 box-border mx-0 bg-white pt-4 ;
            .nut-radio {
              @apply mr-0 mb-0 h-30 px-8 flex justify-between border-b-1-xs;
              
            }

          }
        }

        
      }


    }
  }
  .goodArea {
    @apply flex-1 mt-8 overflow-scroll;
    .scrollViewArea {
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
      gap: 7px;
      width: 100%;
    }
    .good {
      @apply mb-10;
      width: calc(50% - 3.5px);
      .img {
        @apply w-full h-111 bg-brand-898989;
      }
      .goodContent {
        @apply px-4 pt-4;
        .title {
          @apply text-base leading-10 text-brand-2D2E2C font-bold;
        }
        .des {
          @apply text-sm leading-8 text-brand-898989 mt-1;
        }
        .price {
          @apply text-sm leading-8 text-brand-2D2E2C mt-4;
        }
      }
    }
  }
}

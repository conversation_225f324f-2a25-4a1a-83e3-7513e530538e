import { <PERSON>, <PERSON><PERSON><PERSON>iew } from "@tarojs/components";
import { observer } from "mobx-react";
import { useState } from "react";
import { Tabs, Checkbox, Radio } from "@nutui/nutui-react-taro";

import { MainLayout, SearchInput } from "@/components";
import { useI18n } from "@/hook";
import IconFont, { IconNames } from "@/components/iconfont";
import sty from "./index.module.scss";
import { ArrowDown, Checked, Checklist } from "@nutui/icons-react-taro";
import { SafeAreaBottom } from "@/components";
import GoodModal from "../components/goodModal/index";
import { toJump } from "@/utils";

const firstLevelLabel = [
  {
    label: "Waistcoats / Vests",
    value: 1
  },
  {
    label: "Knits",
    value: 2
  },
  {
    label: "Accessories",
    value: 3
  },
  {
    label: "first 4",
    value: 4
  },
  {
    label: "first 5",
    value: 5
  }
];

const secondLevelLabel = [
  {
    label: "ALL",
    value: 1
  },
  {
    label: "Ties",
    value: 2
  },
  {
    label: "Socks",
    value: 3
  },
  {
    label: "Pocket Sqaures",
    value: 4
  },
  {
    label: "first 5",
    value: 5
  }
];

const sortByList = [
  {
    label: "latest style",
    value: 1
  },
  {
    label: "Price High to Low",
    value: 2
  },
  {
    label: "Price Low to High",
    value: 3
  },
  {
    label: "Stock High to Low",
    value: 4
  },
  {
    label: "Stock Low to High",
    value: 5
  }
];

const HomePage = () => {
  const { t } = useI18n();
  const [firstMenu, setFirstMenu] = useState<string | number>(1);
  const [secondValue, setSecondValue] = useState<string | number>(1);

  const [searchValue, setSearchValue] = useState("");
  const [checked, setChecked] = useState(true);

  const [list, setList] = useState(new Array(10).fill({}));

  const [sortByShow, setSortByShow] = useState(false);


  const handleSearchChange = (value: string) => {
    setSearchValue(value);
  };

  const onScrollToUpper = e => {
    console.log("--------onScrollToUpper", e);
  };
  const onScrollToLower = e => {
    console.log("------onScrollToLower", e);
    const res = new Array(10).fill({});
    setList([...list, ...res]);
  };

  const onScroll = e => {
  };

  const info = {
    goods_sn: "C25120A07",
    goods_name: "商品名称",
    goods_price: 1000,
    goods_img: "http://xxx.com/xxx.jpg",
    discription: "商品描述",
    color: "黑色",
    serial_no: "",
    size: "M",
    barcode: ""
  };
  const onAddToBag = () => {};
  const onQueryDetail = () => {};

  return (
    <MainLayout
      initOptions={{
        inited: true,
        initLoading: true,
        loadingFullScreen: true
      }}
      // 使用便捷配置方式
      headerType="withBack"
      showBottomPlaceholder={false}
    >
      <View className={sty.plpPage}>
        <View className={sty.topArea}>
          <View className={sty.searchArea}>
            <SearchInput
              placeholder="Search Product ID"
              onChange={handleSearchChange}
              clearable
              richtIcon={
                <View className="w-20 h-20 bg-black flex-center rounded-sm ml-8">
                  <IconFont name="scan" color={"#fff"} size={28} />
                </View>
              }
            />
          </View>

          <View className={sty.firstLevel}>
            <Tabs
              value={firstMenu}
              onChange={value => {
                setFirstMenu(value);
              }}
              align="left"
            >
              {firstLevelLabel.map(item => (
                <Tabs.TabPane title={item.label} value={item.value} />
              ))}
            </Tabs>
          </View>

          <View className={sty.secondLevel}>
            <Tabs
              value={secondValue}
              onChange={value => {
                setSecondValue(value);
              }}
              align="left"
              activeType="button"
            >
              {secondLevelLabel.map(item => (
                <Tabs.TabPane title={item.label} value={item.value} />
              ))}
            </Tabs>
          </View>

          <View className={sty.threeFilterArea}>
            <View className={sty.storeStock}>
              <Checkbox
                label="In store stock available"
                defaultChecked={checked}
                icon={<IconFont name="checkbox1" size={16} color={"#E1E1E1"} />}
                activeIcon={<IconFont name="checked1" size={16} />}
              />
            </View>
            <View className={sty.rightFilterArea}>
              <View className={sty.filter}>
                Filters
                <IconFont name="filter" size={16} />
              </View>
              <View
                className={sty.sortBy}
                onClick={() => setSortByShow(!sortByShow)}
              >
                Sort By
                <IconFont name="sortBy" size={16} />
              </View>
            </View>

            {/* 绝对定位排序 */}
            <View
              className={sty.panel}
              style={{
                visibility: sortByShow ? "visible" : "hidden",
                maxHeight: sortByShow ? ` calc(100vh - 209px - 94px)` : 0
              }}
            >
              <Radio.Group
                defaultValue={1}
                labelPosition="left"
                style={{ width: "100%" }}
              >
                {sortByList.map((item, index) => (
                  <Radio
                    icon={<View></View>}
                    activeIcon={<IconFont name="check" size={24} />}
                    value={item.value}
                  >
                    {item.label}
                  </Radio>
                ))}
              </Radio.Group>
            </View>
          </View>
        </View>
        <View className={sty.goodArea}>
          <ScrollView
            type="custom"
            className={sty.scrollViewArea}
            scrollY
            scrollWithAnimation
            scrollTop={0}
            style={{ height: "100%" }}
            lowerThreshold={20}
            upperThreshold={20}
            onScrollToUpper={onScrollToUpper} // 使用箭头函数的时候 可以这样写 `onScrollToUpper={this.onScrollToUpper}`
            onScrollToLower={onScrollToLower}
            onScroll={onScroll}
            enableFlex={true}
          >
            {list.map((item, index) => {
              return (
                <View className={sty.good} key={index} onClick={() => {
                  console.log("clickGoods", index);
                  toJump(`/subpackages/mall/pdp/index?goods_sn=${index}`)
                }}>
                  <View className={sty.img}></View>
                  <View className={sty.goodContent}>
                    <View className={sty.title}>
                      Havana navy blue ... -- {index + 1}
                    </View>
                    <View className={sty.des}>Four Seasons Easy Care ...</View>
                    <View className={sty.price}>¥7980</View>
                  </View>
                </View>
              );
            })}
          </ScrollView>
        </View>

        <GoodModal info={info} confirmText="Add to bag" cancelText={'Details'}  onConfirm={onAddToBag}  onCancel={onQueryDetail} visible={false} />

        <SafeAreaBottom />
      </View>
    </MainLayout>
  );
};

export default observer(HomePage);

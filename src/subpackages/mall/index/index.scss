.cash_page {
  position: relative;

  .cash_page_top {
    position: relative;
    padding: 0px 24px;
    width: calc(100vw - 48px);

    .top_banner {
      width: 100vw;
    }

    .switching_guide_container {
      height: 108px;
      width: calc(100vw - 48px);
      // position: relative;
    }
  }

  .cash_page_content {
    // position: absolute;
    // top: 220px;
    background: #f0f0f0;

    width: 100vw;
    height: calc(100vh - 260px);

    .cash_page_member {
      padding: 32px 24px;
      border-radius: 24px;
      // margin-bottom: 16px;
    }

    .goods_list_container {
      background: #fff;
      margin: 0 24px;
      border-radius: 8px 8px 0 0;
      overflow-y: auto;
      width: auto;
      // height: calc(100vh - 260px - 84px - 108px - 92px);
      // //height 行样式会覆盖

      .goods_container_title {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 24px;

        .title_text {
          font-weight: 500;
          font-size: 24px;
          line-height: 28px;
          // margin: 24px;
        }

        .title_op {
          display: flex;
          align-items: center;
          font-size: 20px;
        }
      }

      .goods_list {
        overflow-y: auto;
        background: #fff;
        margin: 0px 24px;
        border-bottom: 1px solid #f0f0f0;

        // height:  calc(100vh - 260px - 84px);
        .goods_item {
          padding: 20px 0;
          border-bottom: 1px solid #f0f0f0;
          display: flex;
          justify-content: space-between;

          .goods_img {
            width: 164px;
            height: 164px;
            background: #f0f0f0;
            border-radius: 12px;
            display: inline-block;
          }

          .goods_detail {
            margin-top: 20px;
            width: 404px;
            margin-left: 20px;
            flex: 1;

            .goods_title {
              font-size: 24px;
              line-height: 32px;
              color: #000000;
              display: flex;
              align-items: center;
              justify-content: space-between;
            }

            .goods_tag {
              margin-top: 10px;
              font-size: 20px;
              line-height: 24px;

              color: #999;
              width: max-content;
              border-radius: 4px;
              padding: 8px 16px;
              border: 1px solid #dddddd;
            }
            .inline {
              display: inline;
            }

            .goods_price {
              margin-top: 10px;
              font-weight: 500;
              font-size: 24px;
              line-height: 36px;
              display: flex;
              align-items: center;
              justify-content: space-between;
              .yuan {
                font-size: 20px;
                display: inline-block;
                padding-right: 4px;
              }

              .gray {
              }
            }
          }

          .goods_edit {
            text-align: right;
          }
        }
      }

      .active_list {
        padding: 24px;

        .active_list_title {
          font-weight: 500;
          font-size: 24px;
          line-height: 28px;
        }

        .active_list_item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding-top: 24px;

          .active_list_item_label {
            font-weight: 400;
            font-size: 20px;
            line-height: 28px;
            color: #818181;
          }

          .active_list_item_value {
            font-weight: 400;
            font-size: 20px;
            line-height: 28px;
          }
        }
      }
    }

    .cash_page_member_empty {
      margin: 24px;
    }

    &.over_hidden {
      overflow: hidden;
    }

    .movable_area {
      width: 100vw;
      height: calc(100vh - 260px - 108px - 92px);
      position: absolute;
      top: 20vh;
      bottom: 0vh;
      right: 0;
      pointer-events: none;
    }
  }

  .cash_page_bottom {
    position: fixed;
    bottom: 0;
    width: calc(100vw - 48px);
    height: 84px;
    padding: 12px 24px;
    box-shadow: 0px -2px 16px 0px #00000014;
    border-top: 1px solid var(--P-5, #f0f0f0);
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #fff;
    z-index: 1000;

    .bottom_left {
      display: flex;
      align-items: center;
      .bottom_left_cash_container {
        font-size: 18px;
        line-height: 28px;
        position: relative;
        .bottom_left_cash_icon {
          width: 40px;
          height: 40px;
          margin: auto;
          display: block;
        }
        .registration_num {
          position: absolute;
          top: 0px;
          right: -10px;
          background: #dfb872;
          border: 1.5px solid #2f2f30;
          width: 34px;
          height: 24px;
          border-radius: 24px;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }
      .line {
        width: 1px;
        height: 24px;
        background: #f0f0f0;
        margin: 0 32px;
      }

      .bottom_left_price_container {
        font-size: 28px;
        color: #333;
      }

      .bottom_left_price {
        font-weight: 400;
        font-size: 20px;
        line-height: 28px;

        .yuan {
          font-size: 20px;
          display: inline-block;
          padding-right: 4px;
        }

        .price {
          font-weight: 500;
          font-size: 32px;
          line-height: 28px;
          letter-spacing: 0%;
          display: inline-block;
        }
      }

      .bottom_left_price_discount {
        font-weight: 400;
        font-size: 18px;
        line-height: 28px;
        letter-spacing: 0%;
        color: #818181;

        .yuan {
          font-size: 14px;
          display: inline-block;
        }

        .price {
          display: inline-block;
        }
      }
    }

    .bottom_right {
      // width: 200px;
      height: 60px;

      .submit_btn {
        width: 260px;
        height: 64px;
        border-radius: 40px;
        border-width: 2.33px;
        line-height: 60px;
        font-size: 20px;
        // padding: 12px 24px;
        color: #fff;
        background: #2f2f30;
        text-align: center;
      }
    }
  }

  .tip {
    display: flex;
    justify-content: center;
    .content {
      display: flex;
      justify-content: center;
      line-height: 34px;
      width: 348px;
    }
  }
}

import { Button, View, Image, MovableA<PERSON>, ScrollView } from "@tarojs/components";
import { observer } from "mobx-react";
import { useCallback, useEffect, useRef, useState } from "react";

import { scanCodeFn, toJump } from "@/utils";
import { MainLayout } from "@/components";
import { useHeaderBarHeight } from "@/hook";
import { InputNumber, Dialog } from "@nutui/nutui-react-taro";
import useStores from "@/hook/useStores";
import EmptyBox from "@/components/Empty";
import classNames from "classnames";
import { toastStore } from "@/mobx";
import { useDidShow } from "@tarojs/taro";

import "./index.scss";

const CashPage = () => {
  const layoutRef = useRef<any>();
  const { headerBarHeight } = useHeaderBarHeight();
  const [deleteDialogViable, setDeleteDialogViable] = useState(false);
  const [goodsItem, setGoodsItem] = useState({});
  const [couponPopupVisible, setCouponPopupVisible] = useState(false);
  const [singleShow, setSingleShow] = useState(false);
  const [moreShow, setMoreShow] = useState(false);

  const [singleRowInfo, setSingleRowInfo] = useState({});

  return (
    <MainLayout
      ref={layoutRef}
      className="cash_page"
      showTopPlaceholder={false}
      style={{
        background: `url('https://bq-pim.oss-cn-hangzhou.aliyuncs.com/cms/sit/assets/image/b24d91c0ce56faa998cb04216f31070f.jpeg?x-oss-process=image/format,webp') no-repeat top center`,
        backgroundSize: "100% auto",
        backgroundColor: "#F9F9F9",
      }}
      // 使用便捷配置方式
      headerType="withBack"
      headerTitle="收银台"
      headerColor="#fff"
      headerBackgroundColor="transparent"></MainLayout>
  );
};
export default observer(CashPage);

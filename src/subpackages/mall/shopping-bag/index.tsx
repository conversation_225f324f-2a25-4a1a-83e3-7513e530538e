import { View } from "@tarojs/components";
import { MainLayout, CustomerCard, ErrorMessage } from "@/components";
import sty from "./index.module.scss";
import IconFont from "@/components/iconfont";
import GoodCard from "../components/goodCard";
import AlterationPopup from "../components/alterationPopup";
import { useState } from "react";
import { useStores } from "@/hook";

const ShoppingBag = () => {
  const { customerStore, shoppingBagStore } = useStores();

  const { currentCustomer } = customerStore;
  return (
    <MainLayout
      initOptions={{
        inited: true,
        initLoading: true,
        loadingFullScreen: true,
      }}
      headerType="withBack"
      showTabbar={true}
      showBottomPlaceholder={false}
      headBarConfig={{}}
    >
      <CustomerCard info={currentCustomer as any} />

      {shoppingBagStore.cartItems.length ? (
        <View className="shopping-bag-list"></View>
      ) : (
        <View className="m-auto">
          <ErrorMessage
            msg="Your bag is empty"
            className="pb-14"
            noIcon
          ></ErrorMessage>
          <View className="border-1-area w-auto px-14 py-7 flex max-w-max mx-auto">
            <IconFont name="shoppingbag" size={22} color="#2d2e2c" />
            <View className="text-center text-base leading-10">Shop Now</View>
          </View>
        </View>
      )}
    </MainLayout>
  );
};

export default ShoppingBag;

import { View } from "@tarojs/components";
import sty from "./index.modules.scss";
import { MainLayout, SafeAreaBottom } from "@/components";
import { Collapse, Checkbox } from "@nutui/nutui-react-taro";
import IconFont from "@/components/iconfont";
import { useState } from "react";
import {useResetState} from 'ahooks'

const list = {
  Color: ["CO000592", "CO001408", "CO001072", "CO001165"],
  Price: ["0-1000", "1001-5000"],
  Season: ["春季", "夏季"],
  Material: ["纯棉"],
  Size: ["SI001583", "SI001584"],
  Style: ["西装"]
};

const Filters = () => {
  const [activeName, setActiveName] = useState(["color", "price"]);
  const [operateList, setOperateList,resetOperateList] = useResetState({
    Color: [],
    Price: [],
    Season: [],
    Material: [],
    Size: [],
    Style: []
  });

  const onCollapseChange = (
    activeName: string | string[],
    name: string,
    isOpen: boolean
  ) => {
    setActiveName(activeName as string[]);
  };
  const onCheckboxChange = (type, value) => {

    setOperateList({
      ...operateList,
      [type]: value
    });
  };

  const onConfirm = () => {
  }
  const onReset = () => {
    resetOperateList()
  }

  
  return (
    <MainLayout
      initOptions={{
        inited: true,
        initLoading: true,
        loadingFullScreen: true
      }}
      headerType="withBack"
      showBottomPlaceholder={false}
    >
      <View className={sty.filtersPage}>
        <View className={sty.mainContent}>
          <Collapse
            defaultActiveName={activeName}
            onChange={onCollapseChange}
          >
            {Object.keys(list).map(type => {
              return (
                <Collapse.Item
                  title={
                    <View className={sty.collapseItemRow}>
                      <View className={sty.collapseItemTitle}>{type}</View>
                      <View className={sty.collapseItemNumber}>{operateList[type].length}</View>
                    </View>
                  }
                  name={type}
                  expandIcon={
                    activeName.includes(type) ? (
                      <IconFont name="closeCatalog" size={24} />
                    ) : (
                      <IconFont name="openCatalog" size={24} />
                    )
                  }
                >
                  <Checkbox.Group
                    labelPosition="right"
                    style={{ width: "100%" }}
                    value={operateList[type]}
                    onChange={value => onCheckboxChange(type, value)}
                  >
                    {list[type].map(value => {
                      return (
                        <Checkbox
                          value={value}
                          icon={<IconFont name="checkbox1" size={16} />}
                          activeIcon={<IconFont name="checked1" size={16} />}
                        >
                          {value}
                        </Checkbox>
                      );
                    })}
                  </Checkbox.Group>
                </Collapse.Item>
              );
            })}
          </Collapse>
        </View>

        <View className={sty.btnArea}>
          <View className={sty.btns}>
            <View className={sty.btn} onClick={onReset}>Clear All</View>
            <View className={sty.btn} onClick={onConfirm}>Applications (3)</View>
          </View>
          <SafeAreaBottom />
        </View>
      </View>
    </MainLayout>
  );
};

export default Filters;

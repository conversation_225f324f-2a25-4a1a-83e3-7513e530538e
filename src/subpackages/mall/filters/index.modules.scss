.filtersPage {
  @apply flex flex-col justify-between h-full;
  .mainContent {
    @apply flex-1 overflow-scroll;
    :global {
      .nut-collapse {
        .nut-collapse-item {
          &::after {
            @apply border-t-1 left-0 right-0;
          }
          .nut-collapse-item-header {
            @apply py-11 px-8;
            &:after {
              @apply border-0;
            }
          }

          .nut-collapse-item-content {
            .nut-collapse-item-content-text {
              @apply px-8 pt-0 pb-10;

              .nut-checkboxgroup {
                .nut-checkbox {
                  @apply pb-10 mb-0;
                  &:last-child {
                    @apply pb-0;
                  }
                  .nut-checkbox-label {
                    @apply ml-4 text-sm leading-8 text-brand-2D2E2C;

                  }
                }
              }
            }


          }
        }
      }
    }

    .collapseItemRow {
      @apply flex items-center ;
      .collapseItemTitle {
        @apply text-base leading-10 text-brand-2D2E2C;
      }
      .collapseItemNumber {
        @apply size-8 rounded-8 bg-brand-EFEFEF text-sm flex justify-center items-center ml-4; 
      }

    }
  }
  .btnArea {
    @apply shadow pt-6 px-8;
    .btns {
      @apply flex justify-between gap-6;
      .btn {
        @apply h-22 flex-1  text-base leading-10 flex justify-center items-center;
        &:first-child {
          @apply border-1-area text-black box-border;
        }
        &:last-child {
          @apply text-white btn-default;
        }
      }
    }
  }
}

import Taro from '@tarojs/taro'
import { cloneDeep } from 'lodash'

import { commonStore, loadingStore, toastStore, userStore } from '@/mobx'
import { UserInfo } from '@/mobx/model/UserInfo'
import dayjs from 'dayjs'

////处理无token 或token过期
const handleToken = (requestParams) => {
  // console.log(requestParams?.Authorization)
  if (requestParams?.Authorization == false) {
    return true
  }

  let { token, tokenExpireTime } = userStore.userInfo
  if (!userStore.userInfo.token) {
    token = Taro.getStorageSync('token')
    tokenExpireTime = Taro.getStorageSync('tokenExpireTime')
  }
  if (!token || token == '') {
    // console.log("!token || token == ''")
    Taro.redirectTo({ url: '/pages/login/index' })
    return false
  }
  if (!tokenExpireTime) {
    // console.log('!tokenExpireTime')
    Taro.redirectTo({ url: '/pages/login/index' })
    return false
  }
  if (tokenExpireTime && tokenExpireTime < dayjs(Date.now()).valueOf()) {
    // console.log('tokenExpireTime < dayjs(Date.now()).valueOf()')
    Taro.redirectTo({ url: '/pages/login/index' })
    Taro.showToast({
      title: '登录已过期,请重新登录',
      icon: 'none',
    })
    return false
  }
  return true
}

//处理头部添加token
const handleAuthorization = (requestParams) => {
  if (requestParams?.Authorization) {
    switch (requestParams?.AuthorizationType) {
      default:
        {
          let { token } = userStore.userInfo
          if (!token) {
            token = Taro.getStorageSync('token')
          }
          if (token) {
            requestParams.header.Authorization = 'Bearer ' + token
          }
        }
        break
    }
  }
  return requestParams
}

const handleHeader = (requestParams: any) => {
  if (!requestParams.header) {
    requestParams.header = {}
  }
  requestParams.header['m2-present'] = commonStore.historyList[0] || ''
  requestParams.header['m2-forward'] = commonStore.historyList[1] || ''
}

//最多重新请求1次
const maxResendTime = 1
// 请求拦截
export const interceptor = (chain) => {
  // 获取参数签名
  const requestParams = chain.requestParams
  const requestParamsBackup = cloneDeep(requestParams)
  //重发次数限制
  requestParamsBackup.resendTime = (requestParamsBackup.resendTime || 0) + 1
  const api = requestParams

  let loadingUUID
  if (api?.loadingFullScreen || api?.loading) {
    loadingUUID = loadingStore.open({
      isFullScreen: api?.loadingFullScreen,
      //如果是小于500ms的快接口就没必要loading了
      showDelay: api.showDelay || 500,
    })
  }

  // 获取网络状态
  Taro.getNetworkType({
    // 获取成功
    success: (res) => {
      // 网络状态
      const hasWeb = res.networkType === 'none'
      // 修改网络状态
      commonStore.isNetworkOK = !hasWeb
      // 如果没有网，取消所有loading
      if (hasWeb) {
        loadingStore.clear()
      }
    },
    // 获取失败
    fail: () => {},
  })
  const validToken = handleToken(requestParams)
  if (!validToken) {
    loadingStore.clear();
    Taro.redirectTo({ url: '/pages/login/index' })
    return Promise.reject(new Error('请求已取消：无效的 Token'))
  }
  handleAuthorization(requestParams)
  handleHeader(requestParams)

  // 响应拦截
  return (
    chain
      .proceed(requestParams)
      .then((res) => {
        if (api?.loadingFullScreen || api?.loading) {
          const timer = setTimeout(() => {
            loadingStore.close(loadingUUID)
            clearTimeout(timer)
          }, 150)
        }
        const { statusCode, data } = res
        switch (statusCode) {
          case 200:
            return data
          default:
            return Promise.reject(res)
        }
      })
      // 请求失败拦截
      .catch((err) => {
        if (api?.loadingFullScreen || api?.loading) {
          const timer = setTimeout(() => {
            loadingStore.close(loadingUUID)
            clearTimeout(timer)
          }, 150)
        }
        const { statusCode, data } = err
        switch (statusCode) {
          case 401:
            // 401 未授权，直接跳转到登录页
            loadingStore.closeInitLoading()
            Taro.redirectTo({ url: '/pages/login/index' })
            Taro.showToast({
              title: '登录已过期，请重新登录',
              icon: 'none',
            })
            return Promise.reject(data)
          default:
            //关闭页面手动初始loading
            loadingStore.closeInitLoading()
            requestParams.showErrorToast !== false && toastStore.show(data?.message || '请求失败')
            // console.log('error requestParams', requestParams)
            return Promise.reject(data)
        }
      })
  )
}

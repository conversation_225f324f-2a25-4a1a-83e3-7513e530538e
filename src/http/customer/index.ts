import { getBaseURL } from "../baseURL";
import { createApis } from '../utils/apiConfig'

// 获取客户模块的baseURL（可能是mock或真实接口）
const customerBaseURL = getBaseURL('customer');

// 小程序环境下，直接使用完整 URL
// 根据配置自动选择mock或真实接口
const customer = createApis({
  regist: {
    url: `${customerBaseURL}/web/mobile/default.php?c=customer&m=member_register`,
    preset: "IMPORTANT_ACTION",
    overrides: {
      loadingText: "加载中...",
      contentType: "application/x-www-form-urlencoded",
    },
  },
	searchCustomers:{
		 url: `${customerBaseURL}/web/mobile/default.php?c=customer&m=member_list_query`,
    preset: "IMPORTANT_ACTION",
    overrides: {
      loadingText: "加载中...",
      contentType: "application/x-www-form-urlencoded",
    },
	},
	  queryCustomerInfo:{
		 url: `${customerBaseURL}/web/mobile/default.php?c=customer&m=member_detail_query`,
    preset: "IMPORTANT_ACTION",
    overrides: {
      loadingText: "加载中...",
      contentType: "application/x-www-form-urlencoded",
    },
	},

  updateMemberInfo:{
		 url: `${customerBaseURL}/web/mobile/default.php?c=customer&m=member_update`,
    preset: "IMPORTANT_ACTION",
    overrides: {
      loadingText: "加载中...",
      contentType: "application/x-www-form-urlencoded",
    },
	},

  // ==================== Address 地址相关接口 ====================

  // 获取省份列表
  getProvinceList: {
    url: `${customerBaseURL}/web/mobile/default.php?c=address&m=province_list`,
    preset: "GET_WITH_LOADING",
    overrides: {
      loadingText: "加载省份中...",
      contentType: "application/x-www-form-urlencoded",
    },
  },

  // 获取城市列表
  getCityList: {
    url: `${customerBaseURL}/web/mobile/default.php?c=address&m=city_list`,
    preset: "GET_WITH_LOADING",
    overrides: {
      loadingText: "加载城市中...",
      contentType: "application/x-www-form-urlencoded",
    },
  },

  // 获取区县列表
  getDistrictList: {
    url: `${customerBaseURL}/web/mobile/default.php?c=address&m=district_list`,
    preset: "GET_WITH_LOADING",
    overrides: {
      loadingText: "加载区县中...",
      contentType: "application/x-www-form-urlencoded",
    },
  },
})

export default customer;

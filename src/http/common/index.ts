import { getBaseURL } from "../baseURL";
import { createApis } from "../utils/apiConfig";

// 获取通用模块的baseURL（可能是mock或真实接口）
const commonBaseURL = getBaseURL('common');

// 小程序环境下，直接使用完整 URL
// 根据配置自动选择mock或真实接口
const common = createApis({
  default: {
    url: `${commonBaseURL}/web/mobile/default.php`,
    preset: "IMPORTANT_ACTION",
    overrides: {
      loadingText: "加载中...",
      contentType: "application/x-www-form-urlencoded"
    }
  },
  getDianyuanDetail: {
    url: `${commonBaseURL}/web/mobile/default.php?c=user&m=user_info`,
    preset: "IMPORTANT_ACTION",
    overrides: {
      loadingText: "加载中...",
      contentType: "application/x-www-form-urlencoded"
    }
  },
  getPaymentList: {
    url: `${commonBaseURL}/web/mobile/default.php?c=payment_terminal&m=list`,
    preset: "IMPORTANT_ACTION",
    overrides: {
      loadingText: "加载中...",
      contentType: "application/x-www-form-urlencoded"
    }
  },
  getStoreList: {
    url: `${commonBaseURL}/web/mobile/default.php?c=store&m=list`,
    preset: "IMPORTANT_ACTION",
    overrides: {
      loadingText: "加载中...",
      contentType: "application/x-www-form-urlencoded"
    }
  },

  getSale: {
    url: `${commonBaseURL}/web/mobile/default.php?c=user&m=home_yj`,
    preset: "IMPORTANT_ACTION",
    overrides: {
      loadingText: "加载中...",
      contentType: "application/x-www-form-urlencoded"
    }
  },
  onChangeDeviceInfo: {
    url: `${commonBaseURL}/web/mobile/default.php?c=user&m=setting_save`,
    preset: "IMPORTANT_ACTION",
    overrides: {
      loadingText: "加载中...",
      contentType: "application/x-www-form-urlencoded"
    }
  },
  onLogOut: {
    url: `${commonBaseURL}/web/mobile/default.php?c=user&m=logout`,
    preset: "IMPORTANT_ACTION",
    overrides: {
      loadingText: "加载中...",
      contentType: "application/x-www-form-urlencoded"
    }
  },

});

export default common;

import { baseURL } from '../baseURL'
import creator from '../creator'

export type HttpMethod = 'POST' | 'GET' | 'PUT' | 'DELETE'
export type AuthorizationType = 'cms' | undefined

/**
 * API配置接口
 */
export interface ApiConfig {
  url: string | object
  method: HttpMethod
  Authorization: boolean // 改为必需字段，确保类型兼容
  AuthorizationType?: AuthorizationType
  header?: any
  loading: boolean // 改为必需字段，确保类型兼容
  loadingFullScreen?: boolean
  contentType?: string
  showErrorToast?: boolean
  showDelay?: number
  loadingText?: string
}

/**
 * 预设的API配置模板
 */
export const API_PRESETS = {
  // 普通GET请求，带loading
  GET_WITH_LOADING: {
    method: 'GET' as HttpMethod,
    Authorization: true,
    loading: true,
    loadingFullScreen: false,
    showDelay: 500,
    contentType: 'application/json'
  },

  // 普通POST请求，带loading
  POST_WITH_LOADING: {
    method: 'POST' as HttpMethod,
    Authorization: true,
    loading: true,
    loadingFullScreen: false,
    showDelay: 500,
    contentType: 'application/x-www-form-urlencoded'
  },

  // 重要操作（如登录、提交表单），全屏loading
  IMPORTANT_ACTION: {
    method: 'POST' as HttpMethod,
    Authorization: false,
    loading: true,
    loadingFullScreen: true,
    showDelay: 0, // 重要操作立即显示loading
     contentType: 'application/x-www-form-urlencoded'
  },

  // 快速查询，无loading（避免闪烁）
  QUICK_QUERY: {
    method: 'GET' as HttpMethod,
    Authorization: true,
    loading: false,
    contentType: 'application/json'
  },

  // 后台操作（如刷新token），无loading
  BACKGROUND_ACTION: {
    method: 'POST' as HttpMethod,
    Authorization: false,
    loading: false,
    contentType: 'application/json'
  },

  // 文件上传，全屏loading
  FILE_UPLOAD: {
    method: 'POST' as HttpMethod,
    Authorization: true,
    loading: true,
    loadingFullScreen: true,
    showDelay: 0,
    contentType: 'multipart/form-data'
  },

  // 表单提交，带loading
  FORM_SUBMIT: {
    method: 'POST' as HttpMethod,
    Authorization: true,
    loading: true,
    loadingFullScreen: false,
    showDelay: 200,
    contentType: 'application/x-www-form-urlencoded'
  }
}

/**
 * 创建API配置的工具函数
 * @param url API地址
 * @param preset 预设配置
 * @param overrides 覆盖配置
 * @returns 完整的API配置
 */
export function createApiConfig(
  url: string,
  preset: keyof typeof API_PRESETS,
  overrides: Partial<ApiConfig> = {}
): ApiConfig {
	// console.log('url',url);
	
  return {
    url: url.startsWith('http') ? url : `${baseURL}${url}`,
    ...API_PRESETS[preset],
    ...overrides
  }
}

/**
 * 快速创建API的工具函数
 * @param url API地址
 * @param preset 预设配置
 * @param overrides 覆盖配置
 * @returns creator函数
 */
export function createApi<D = any, R = any>(
  url: string,
  preset: keyof typeof API_PRESETS,
  overrides: Partial<ApiConfig> = {}
) {
  const config = createApiConfig(url, preset, overrides)
  return creator<D, R>(config)
}

/**
 * 批量创建API的工具函数
 * @param apis API配置对象
 * @returns API对象
 */
export function createApis<T extends Record<string, { url: string; preset: keyof typeof API_PRESETS; overrides?: Partial<ApiConfig> }>>(
  apis: T
): { [K in keyof T]: ReturnType<typeof creator> } {
  const result = {} as any
  
  for (const [key, config] of Object.entries(apis)) {
    result[key] = createApi(config.url, config.preset, config.overrides)
  }
  
  return result
}

/**
 * 常用的loading配置
 */
export const LOADING_CONFIGS = {
  // 立即显示loading
  IMMEDIATE: { showDelay: 0 },
  
  // 快速接口，延迟显示loading避免闪烁
  FAST_API: { showDelay: 500 },
  
  // 慢接口，稍微延迟显示
  SLOW_API: { showDelay: 200 },
  
  // 全屏loading
  FULLSCREEN: { loadingFullScreen: true, showDelay: 0 },
  
  // 普通loading
  NORMAL: { loadingFullScreen: false, showDelay: 500 },
  
  // 无loading
  NONE: { loading: false }
}

/**
 * 为现有API添加loading配置的工具函数
 * @param apiFunction 现有的API函数
 * @param loadingConfig loading配置
 * @returns 包装后的API函数
 */
export function withLoading<T extends (...args: any[]) => Promise<any>>(
  apiFunction: T,
  loadingConfig: Partial<{ loading: boolean; loadingFullScreen: boolean; showDelay: number }>
): T {
  return ((...args: any[]) => {
    // 如果第二个参数是options对象，则合并loading配置
    if (args.length >= 2 && typeof args[1] === 'object') {
      args[1] = { ...loadingConfig, ...args[1] }
    } else if (args.length === 1) {
      args.push(loadingConfig)
    } else {
      args[1] = loadingConfig
    }
    
    return apiFunction(...args)
  }) as T
}

// 导出一些常用的配置组合
export const COMMON_CONFIGS = {
  // 登录相关
  LOGIN: createApiConfig('', 'IMPORTANT_ACTION', { 
    loadingText: '登录中...',
    contentType: 'application/x-www-form-urlencoded'
  }),
  
  // 数据查询
  QUERY: createApiConfig('', 'GET_WITH_LOADING'),
  
  // 数据提交
  SUBMIT: createApiConfig('', 'POST_WITH_LOADING'),
  
  // 快速查询
  QUICK: createApiConfig('', 'QUICK_QUERY'),
  
  // 后台操作
  BACKGROUND: createApiConfig('', 'BACKGROUND_ACTION')
}

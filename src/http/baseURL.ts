import { getModuleBaseURL } from '@/config/mockConfig';

const apiEnv: any = process.env.API_ENV

//接口域名
export const _baseURL = {
  dev: 'https://pos-suitsupply.mybaiqiu.com',
  uat: 'https://pos-sit.ibaiqiu.cn',
  production: 'https://pos-sit.ibaiqiu.cn',
  mock: 'http://127.0.0.1:9527'
}

// 默认baseURL（向后兼容）
export const baseURL = _baseURL[apiEnv]

// 按模块获取baseURL的函数
export const getBaseURL = (module: 'auth' | 'customer' | 'order' | 'common' | 'setting') => {
  return getModuleBaseURL(module);
};

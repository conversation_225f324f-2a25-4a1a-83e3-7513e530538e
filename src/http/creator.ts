import Taro from "@tarojs/taro";

export type AuthorizationType = "auth" | undefined;
interface obj {
  type?: string;
  url: string | object;
  method: "POST" | "GET" | "PUT" | "DELETE";
  Authorization: boolean;
  AuthorizationType?: AuthorizationType;
  header?: any;
  loading: boolean;
  loadingFullScreen?: boolean;
  contentType?: string;
  showErrorToast?: boolean;
  // 新增loading相关配置
  showDelay?: number; // loading延迟显示时间，默认500ms
  loadingText?: string; // 自定义loading文本
}

interface creatorOptions {
  //是否loading
  loading?: boolean;
  //是否全屏loading
  loadingFullScreen?: boolean;
  //loading延迟显示时间
  showDelay?: number;
  //自定义loading文本
  loadingText?: string;
  //用于生成url的参数
  params?: any;
}

function creator<D = any, R = any>(apiObj: obj) {
  const request = (data?: D, options?: Partial<creatorOptions>, postData?: any) => {
    const { params, ...restOptions } = Object.assign({}, options);
    console.log("====================", apiObj);

    // 合并API配置和运行时选项，运行时选项优先级更高
    const finalConfig = {
      ...apiObj,
      ...restOptions,
      // loading相关配置可以在运行时覆盖
      loading: restOptions.loading !== undefined ? restOptions.loading : apiObj.loading,
      loadingFullScreen: restOptions.loadingFullScreen !== undefined ? restOptions.loadingFullScreen : apiObj.loadingFullScreen,
      showDelay: restOptions.showDelay !== undefined ? restOptions.showDelay : apiObj.showDelay,
      loadingText: restOptions.loadingText || apiObj.loadingText,
    };

    console.log("finalConfig", finalConfig);
    const token = Taro.getStorageSync("token");

    const config: Taro.request.Option<R> = {
      ...finalConfig,
      url: typeof apiObj.url === "function" ? apiObj.url(params || {}) : `${apiObj.url}${postData || ""}`,
      data: data || {},
      header: {
        "content-type": apiObj.contentType || "application/json",
        Authorization: !apiObj.AuthorizationType && token,
        ...apiObj.header,
      },
      method: apiObj.method,
    };
    // console.log("config", config);

    return Taro.request(config);
  };

  return request;
}

export default creator;

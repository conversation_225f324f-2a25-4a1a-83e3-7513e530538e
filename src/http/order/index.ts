import { getBaseURL } from "../baseURL";
import { createApis } from '../utils/apiConfig'

// 获取订单模块的baseURL（可能是mock或真实接口）
const orderBaseURL = getBaseURL('order');

// 小程序环境下，直接使用完整 URL
// 根据配置自动选择mock或真实接口
const order = createApis({
  // ==================== 商品相关接口 ====================

  // 一级分类接口
  getCategoryList: {
    url: `${orderBaseURL}/web/mobile/default.php?c=goods&m=category_list`,
    preset: "GET_WITH_LOADING",
    overrides: {
      loadingText: "加载分类中...",
      contentType: "application/x-www-form-urlencoded",
    },
  },

  // 二级分类接口
  getSubCategoryList: {
    url: `${orderBaseURL}/web/mobile/default.php?c=goods&m=sub_category_list`,
    preset: "GET_WITH_LOADING",
    overrides: {
      loadingText: "加载子分类中...",
      contentType: "application/x-www-form-urlencoded",
    },
  },

  // filter接口
  getFilterOptions: {
    url: `${orderBaseURL}/web/mobile/default.php?c=goods&m=filter_options`,
    preset: "GET_WITH_LOADING",
    overrides: {
      loadingText: "加载筛选选项中...",
      contentType: "application/x-www-form-urlencoded",
    },
  },

  // 商品搜索接口
  searchGoods: {
    url: `${orderBaseURL}/web/mobile/default.php?c=goods&m=search`,
    preset: "POST_WITH_LOADING",
    overrides: {
      loadingText: "搜索商品中...",
      contentType: "application/x-www-form-urlencoded",
    },
  },

  // 商品扫码查询接口
  scanGoods: {
    url: `${orderBaseURL}/web/mobile/default.php?c=goods&m=scan_query`,
    preset: "GET_WITH_LOADING",
    overrides: {
      loadingText: "扫码查询中...",
      contentType: "application/x-www-form-urlencoded",
    },
  },

  // 商品详情接口
  getGoodsDetail: {
    url: `${orderBaseURL}/web/mobile/default.php?c=goods&m=detail`,
    preset: "GET_WITH_LOADING",
    overrides: {
      loadingText: "加载商品详情中...",
      contentType: "application/x-www-form-urlencoded",
    },
  },

  // 商品尺码接口
  getGoodsSizeList: {
    url: `${orderBaseURL}/web/mobile/default.php?c=goods&m=size_list`,
    preset: "GET_WITH_LOADING",
    overrides: {
      loadingText: "加载尺码信息中...",
      contentType: "application/x-www-form-urlencoded",
    },
  },

  // 临店库存接口
  getStoreStock: {
    url: `${orderBaseURL}/web/mobile/default.php?c=goods&m=store_stock`,
    preset: "GET_WITH_LOADING",
    overrides: {
      loadingText: "查询库存中...",
      contentType: "application/x-www-form-urlencoded",
    },
  },

  // ==================== 订单相关接口 ====================

  // 获取订单历史列表 - 更新为POST请求
  getOrderHistory: {
    url: `${orderBaseURL}/web/mobile/default.php?c=order&m=history_list`,
    preset: "POST_WITH_LOADING",
    overrides: {
      loadingText: "加载订单历史中...",
      contentType: "application/x-www-form-urlencoded",
    },
  },

  // 搜索订单
  searchOrders: {
    url: `${orderBaseURL}/web/mobile/default.php?c=order&m=search`,
    preset: "GET_WITH_LOADING",
    overrides: {
      loadingText: "搜索订单中...",
      contentType: "application/x-www-form-urlencoded",
    },
  },

  // 获取订单详情
  getOrderDetail: {
    url: `${orderBaseURL}/web/mobile/default.php?c=order&m=detail`,
    preset: "GET_WITH_LOADING",
    overrides: {
      loadingText: "加载订单详情中...",
      contentType: "application/x-www-form-urlencoded",
    },
  },

  // 检索订单（Retrieve）
  retrieveOrder: {
    url: `${orderBaseURL}/web/mobile/default.php?c=order&m=retrieve`,
    preset: "IMPORTANT_ACTION",
    overrides: {
      loadingText: "检索订单中...",
      contentType: "application/x-www-form-urlencoded",
    },
  },

  // ==================== 购物车相关接口 ====================

  // 购物车金额计算接口
  calculateCartTotal: {
    url: `${orderBaseURL}/web/mobile/default.php?c=cart&m=calculate`,
    preset: "POST_WITH_LOADING",
    overrides: {
      loadingText: "计算金额中...",
      contentType: "application/x-www-form-urlencoded",
    },
  },

  // 购物车库存校验接口
  validateCartStock: {
    url: `${orderBaseURL}/web/mobile/default.php?c=cart&m=validate_stock`,
    preset: "POST_WITH_LOADING",
    overrides: {
      loadingText: "校验库存中...",
      contentType: "application/x-www-form-urlencoded",
    },
  },
})

export default order;

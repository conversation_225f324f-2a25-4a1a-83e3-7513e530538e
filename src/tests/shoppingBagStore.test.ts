// 购物车 Store 测试文件
// 注意：这是一个示例测试文件，实际项目中需要配置测试环境

import { shoppingBagStore, ShoppingCartItem } from "@/mobx/store/mall";
import { OrderGoods } from "@/mobx/model/Order";

// 模拟商品数据
const mockProduct: Partial<OrderGoods> = {
  goods_sn: "C25120A07",
  goods_name: "Navy Blue Suit",
  goods_price: 2500,
  goods_img: "https://example.com/image.jpg",
  discription: "Premium navy blue wool suit",
  color: "Navy",
  serial_no: "SN001",
  size: "M",
  barcode: "1234567890001",
  status: "可售",
  detail: {
    MasterProductId: "H7326A",
    Material: "Egyptian Cotton",
  },
  detail_img: [],
  cm_url: "",
  alteration_url: "",
};

// 测试函数
export const testShoppingBagStore = async () => {
  console.log("=== 开始测试 ShoppingBagStore ===");

  try {
    // 1. 测试初始状态
    console.log("1. 测试初始状态");
    console.log("购物车是否为空:", shoppingBagStore.isEmpty);
    console.log("商品总数:", shoppingBagStore.cartItems.length);
    console.log("总数量:", shoppingBagStore.totalQuantity);

    // 2. 测试添加商品
    console.log("\n2. 测试添加商品");
    const addResult = await shoppingBagStore.addToCart(mockProduct, 2);
    console.log("添加结果:", addResult);
    console.log("购物车商品数:", shoppingBagStore.cartItems.length);
    console.log("总数量:", shoppingBagStore.totalQuantity);

    if (shoppingBagStore.cartItems.length > 0) {
      const firstItem = shoppingBagStore.cartItems[0];
      console.log("第一个商品:", {
        name: firstItem.goods_name,
        quantity: firstItem.quantity,
        subtotal: firstItem.subtotal,
        selected: firstItem.selected,
      });

      // 3. 测试更新数量
      console.log("\n3. 测试更新数量");
      const updateResult = await shoppingBagStore.updateQuantity(firstItem.cartItemId, 3);
      console.log("更新结果:", updateResult);
      console.log("更新后数量:", firstItem.quantity);
      console.log("更新后小计:", firstItem.subtotal);

      // 4. 测试设置折扣
      console.log("\n4. 测试设置折扣");
      shoppingBagStore.setItemDiscount(firstItem.cartItemId, 0.8); // 8折
      console.log("设置8折后:");
      console.log("折扣:", firstItem.discount);
      console.log("折扣价:", firstItem.discountPrice);
      console.log("小计:", firstItem.subtotal);

      // 5. 测试选择状态
      console.log("\n5. 测试选择状态");
      console.log("选中状态:", firstItem.selected);
      shoppingBagStore.toggleItemSelection(firstItem.cartItemId);
      console.log("切换后选中状态:", firstItem.selected);
      console.log("选中商品数:", shoppingBagStore.selectedItems.length);

      // 6. 测试计算结果
      console.log("\n6. 测试计算结果");
      console.log("最后计算结果:", shoppingBagStore.lastCalculation);

      // 7. 测试查询方法
      console.log("\n7. 测试查询方法");
      const foundByBarcode = shoppingBagStore.findItemByBarcode(mockProduct.barcode!);
      console.log("根据条码查找:", foundByBarcode ? "找到" : "未找到");
      
      const quantity = shoppingBagStore.getItemQuantity(mockProduct.barcode!);
      console.log("商品数量:", quantity);
      
      const inCart = shoppingBagStore.isItemInCart(mockProduct.barcode!);
      console.log("是否在购物车:", inCart);

      const summary = shoppingBagStore.getCartSummary();
      console.log("购物车摘要:", summary);

      // 8. 测试移除商品
      console.log("\n8. 测试移除商品");
      shoppingBagStore.removeItem(firstItem.cartItemId);
      console.log("移除后商品数:", shoppingBagStore.cartItems.length);
    }

    // 9. 测试添加多个商品
    console.log("\n9. 测试添加多个商品");
    await shoppingBagStore.addToCart(mockProduct, 1);
    await shoppingBagStore.addToCart({
      ...mockProduct,
      goods_sn: "C25120A08",
      goods_name: "Black Formal Jacket",
      barcode: "1234567890002",
    }, 2);

    console.log("添加多个商品后:");
    console.log("商品总数:", shoppingBagStore.cartItems.length);
    console.log("总数量:", shoppingBagStore.totalQuantity);

    // 10. 测试全选
    console.log("\n10. 测试全选");
    shoppingBagStore.toggleSelectAll(true);
    console.log("全选后选中商品数:", shoppingBagStore.selectedItems.length);

    // 11. 测试清空购物车
    console.log("\n11. 测试清空购物车");
    shoppingBagStore.clearCart();
    console.log("清空后商品数:", shoppingBagStore.cartItems.length);
    console.log("清空后是否为空:", shoppingBagStore.isEmpty);

    console.log("\n=== 测试完成 ===");
  } catch (error) {
    console.error("测试过程中出现错误:", error);
  }
};

// 导出测试函数，可以在控制台中调用
// 使用方法：在浏览器控制台中运行 testShoppingBagStore()
if (typeof window !== "undefined") {
  (window as any).testShoppingBagStore = testShoppingBagStore;
}

export default testShoppingBagStore;

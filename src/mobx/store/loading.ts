import { makeAutoObservable } from 'mobx'

import { generateUUID } from '@/utils'
import { isString } from 'lodash'

interface LoadingItem {
  uuid: string
  //是否全屏
  isFullScreen: boolean
  //是否是mainlayout手动控制的加载loading error时需要手动清掉
  isInitLoading: boolean
  //延迟展示时间
  showDelay?: number
  //是否展示
  isShow: boolean
}
class LoadingStore {
  constructor() {
    makeAutoObservable(this)
  }
  //记录loading随机串
  loadingList: Array<LoadingItem> = []
  get openTimes() {
    return this.loadingList.filter((i) => i.isShow)?.length
  }
  //全局控制是否展示loading
  globalShow = true
  get isShow() {
    return this.openTimes > 0 && this.globalShow
  }
  //全局控制是否全屏Loading
  forceFullScreen = false
  //是否全屏 详见readme zindex管理
  get isFullScreen() {
    return this.forceFullScreen || !!this.loadingList.find((i) => i.isFullScreen)
  }

  open(options: Partial<Omit<LoadingItem, 'uuid'>> = {}) {
    const { isFullScreen = false, isInitLoading = false, showDelay = 0 } = options
		// console.log("isFullScreen", isFullScreen);
		
    const uuid = generateUUID()
    let item = { uuid, isFullScreen, isInitLoading, isShow: !showDelay, showDelay }
    this.loadingList.push(item)
    if (showDelay) {
      setTimeout(() => {
        let item = this.loadingList.find((i) => i.uuid === uuid)
        item && (item.isShow = true)
      }, showDelay)
    }
    return uuid
  }

  //打开全屏
  openFullScreen(options: Partial<Omit<LoadingItem, 'uuid' | 'isFullScreen'>> = {}) {
		// console.log(111);
		
    return this.open({ isFullScreen: true, ...options })
  }

  close(uuid: string) {
    if (!uuid || !isString(uuid)) {
      return
    }
		// console.log("loadingList", this.loadingList);
		
    this.loadingList = this.loadingList.filter((i) => i.uuid !== uuid)
  }

  //手动控制的加载loading error时需要手动清掉
  closeInitLoading() {
    this.loadingList = this.loadingList.filter((i) => !i.isInitLoading)
  }

  clear() {
    this.loadingList = []
  }
}

const loadingStore = new LoadingStore()

export default loadingStore

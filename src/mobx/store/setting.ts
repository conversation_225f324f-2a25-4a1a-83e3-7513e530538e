import Taro from "@tarojs/taro";
import { makeAutoObservable } from "mobx";
import http from "@/http";
import toastStore from "./toast";
import userStore from "./user";
interface DeviceType {
  id: string;
  name: string;
}

class SettingStore {
  constructor() {
    makeAutoObservable(this);
  }
  paymentList: DeviceType[] = [];
  shopList: DeviceType[] = [];
  async getPaymentList() {
    try {
      const res: any = await http.common.getPaymentList({});
      this.paymentList = res.data;

      if (res.code !== 0) {
        toastStore.show({ icon: "error", content: res.msg });
      }
    } catch (error) {
      toastStore.show({ icon: "error", content: error.msg });
    }
  }

  async getShopList() {
    try {
      const res: any = await http.common.getStoreList({});
      this.shopList = res.data;
      if (res.code !== 0) {
        toastStore.show({ icon: "error", content: res.msg });
      }
    } catch (error) {
      toastStore.show({ icon: "error", content: error.msg });
    }
  }
}

const settingStore = new SettingStore();
export default settingStore;

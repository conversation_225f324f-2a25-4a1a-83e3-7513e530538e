import { makeAutoObservable } from "mobx";
import { makePersistable } from "mobx-persist-store";
import http from "@/http";
import toastStore from "./toast";
import { OrderGoods } from "@/mobx/model/Order";

// 购物车商品接口，基于mock/order.js中的商品数据结构
export interface ShoppingCartItem extends OrderGoods {
  // 购物车特有字段
  quantity: number; // 商品数量
  selected: boolean; // 是否选中
  discount?: number; // 单行商品折扣 (0-1之间的数值，如0.8表示8折)
  discountPrice?: number; // 折扣后价格
  subtotal: number; // 小计金额
  addTime: string; // 添加时间
  cartItemId: string; // 购物车项目唯一ID
}

// 购物车计算结果接口
export interface CartCalculation {
  totalQuantity: number; // 总数量
  totalAmount: number; // 总金额
  discountAmount: number; // 折扣金额
  finalAmount: number; // 最终金额
}

// 库存校验结果接口
export interface StockValidation {
  isValid: boolean;
  message: string;
  availableStock: number;
}

class ShoppingBagStore {
  constructor() {
    makeAutoObservable(this);
    makePersistable(this, {
      name: "ShoppingBagStore",
      properties: ["cartItems"],
      expireIn: 0, // 永不过期
    }).then();
  }

  // 购物车商品列表
  cartItems: ShoppingCartItem[] = [];

  // 是否正在计算金额
  isCalculating: boolean = false;

  // 最后一次计算结果
  lastCalculation: CartCalculation = {
    totalQuantity: 0,
    totalAmount: 0,
    discountAmount: 0,
    finalAmount: 0,
  };

  // ==================== Getters ====================

  // 获取购物车商品总数量
  get totalQuantity(): number {
    return this.cartItems.reduce((total, item) => total + (item.selected ? item.quantity : 0), 0);
  }

  // 获取选中的商品列表
  get selectedItems(): ShoppingCartItem[] {
    return this.cartItems.filter(item => item.selected);
  }

  // 获取购物车是否为空
  get isEmpty(): boolean {
    return this.cartItems.length === 0;
  }

  // 获取是否有选中商品
  get hasSelectedItems(): boolean {
    return this.selectedItems.length > 0;
  }

  // ==================== 商品管理方法 ====================

  // 添加商品到购物车
  async addToCart(product: Partial<OrderGoods>, quantity: number = 1): Promise<boolean> {
    try {
      // 1. 校验库存
      const stockValidation = await this.validateStock(product.barcode!, quantity);
      if (!stockValidation.isValid) {
        toastStore.show({ icon: "error", content: stockValidation.message });
        return false;
      }

      // 2. 检查是否已存在相同商品（相同barcode）
      const existingItemIndex = this.cartItems.findIndex(
        item => item.barcode === product.barcode
      );

      if (existingItemIndex !== -1) {
        // 更新现有商品数量
        const existingItem = this.cartItems[existingItemIndex];
        const newQuantity = existingItem.quantity + quantity;

        // 再次校验库存
        const newStockValidation = await this.validateStock(product.barcode!, newQuantity);
        if (!newStockValidation.isValid) {
          toastStore.show({ icon: "error", content: newStockValidation.message });
          return false;
        }

        this.updateQuantity(existingItem.cartItemId, newQuantity);
      } else {
        // 添加新商品
        const cartItem: ShoppingCartItem = {
          ...product as OrderGoods,
          quantity,
          selected: true,
          subtotal: (product.goods_price || 0) * quantity,
          addTime: new Date().toISOString(),
          cartItemId: this.generateCartItemId(),
        };

        this.cartItems.push(cartItem);
      }

      // 3. 重新计算金额
      await this.calculateTotal();

      toastStore.show({ icon: "success", content: "商品已添加到购物车" });
      return true;
    } catch (error) {
      console.error("添加商品到购物车失败:", error);
      toastStore.show({ icon: "error", content: "添加商品失败" });
      return false;
    }
  }

  // 移除商品
  removeItem(cartItemId: string): void {
    const index = this.cartItems.findIndex(item => item.cartItemId === cartItemId);
    if (index !== -1) {
      this.cartItems.splice(index, 1);
      this.calculateTotal();
      toastStore.show({ icon: "success", content: "商品已移除" });
    }
  }

  // 更新商品数量
  async updateQuantity(cartItemId: string, quantity: number): Promise<boolean> {
    const item = this.cartItems.find(item => item.cartItemId === cartItemId);
    if (!item) return false;

    if (quantity <= 0) {
      this.removeItem(cartItemId);
      return true;
    }

    // 校验库存
    const stockValidation = await this.validateStock(item.barcode, quantity);
    if (!stockValidation.isValid) {
      toastStore.show({ icon: "error", content: stockValidation.message });
      return false;
    }

    item.quantity = quantity;
    this.updateItemSubtotal(item);
    await this.calculateTotal();
    return true;
  }

  // 切换商品选中状态
  toggleItemSelection(cartItemId: string): void {
    const item = this.cartItems.find(item => item.cartItemId === cartItemId);
    if (item) {
      item.selected = !item.selected;
      this.calculateTotal();
    }
  }

  // 全选/取消全选
  toggleSelectAll(selected: boolean): void {
    this.cartItems.forEach(item => {
      item.selected = selected;
    });
    this.calculateTotal();
  }

  // 设置单行商品折扣
  setItemDiscount(cartItemId: string, discount: number): void {
    const item = this.cartItems.find(item => item.cartItemId === cartItemId);
    if (item) {
      // 折扣范围校验 (0-1之间)
      item.discount = Math.max(0, Math.min(1, discount));
      this.updateItemSubtotal(item);
      this.calculateTotal();
      toastStore.show({ icon: "success", content: `已设置${Math.round(discount * 100)}%折扣` });
    }
  }

  // 清空购物车
  clearCart(): void {
    this.cartItems = [];
    this.lastCalculation = {
      totalQuantity: 0,
      totalAmount: 0,
      discountAmount: 0,
      finalAmount: 0,
    };
    toastStore.show({ icon: "success", content: "购物车已清空" });
  }

  // ==================== 私有辅助方法 ====================

  // 生成购物车项目唯一ID
  private generateCartItemId(): string {
    return `cart_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }

  // 更新商品小计
  private updateItemSubtotal(item: ShoppingCartItem): void {
    const originalPrice = item.goods_price * item.quantity;
    if (item.discount && item.discount < 1) {
      item.discountPrice = item.goods_price * item.discount;
      item.subtotal = item.discountPrice * item.quantity;
    } else {
      item.discountPrice = undefined;
      item.subtotal = originalPrice;
    }
  }

  // 校验库存
  private async validateStock(barcode: string, quantity: number): Promise<StockValidation> {
    try {
      // 调用尺码接口获取库存信息
      const response: any = await http.order.getGoodsSizeList({ barcode });

      if (response.code === 0 && response.data && Array.isArray(response.data) && response.data.length > 0) {
        // 找到对应barcode的尺码信息
        const sizeInfo = response.data.find((size: any) => size.barcode === barcode);

        if (sizeInfo) {
          const availableStock = sizeInfo.store_stock || 0;

          if (quantity > availableStock) {
            return {
              isValid: false,
              message: `库存不足，当前库存：${availableStock}`,
              availableStock,
            };
          }

          return {
            isValid: true,
            message: "库存充足",
            availableStock,
          };
        }
      }

      // 如果没有找到库存信息，默认允许添加但给出警告
      return {
        isValid: true,
        message: "无法获取库存信息，请注意库存状况",
        availableStock: 0,
      };
    } catch (error) {
      console.error("库存校验失败:", error);
      // 网络错误时默认允许添加
      return {
        isValid: true,
        message: "库存校验失败，请注意库存状况",
        availableStock: 0,
      };
    }
  }

  // 计算购物车总金额（调用后端接口）
  async calculateTotal(): Promise<void> {
    if (this.cartItems.length === 0) {
      this.lastCalculation = {
        totalQuantity: 0,
        totalAmount: 0,
        discountAmount: 0,
        finalAmount: 0,
      };
      return;
    }

    this.isCalculating = true;

    try {
      // 准备发送给后端的商品数据
      const cartData = this.selectedItems.map(item => ({
        goods_sn: item.goods_sn,
        barcode: item.barcode,
        quantity: item.quantity,
        price: item.goods_price,
        discount: item.discount || 1,
      }));

      // 调用后端计算接口
      const response: any = await http.order.calculateCartTotal({ items: cartData });

      if (response.code === 0 && response.data) {
        this.lastCalculation = {
          totalQuantity: response.data.totalQuantity,
          totalAmount: response.data.totalAmount,
          discountAmount: response.data.discountAmount,
          finalAmount: response.data.finalAmount,
        };
      } else {
        // 接口返回错误时使用本地计算
        this.lastCalculation = this.calculateTotalLocally();
      }
    } catch (error) {
      console.error("计算金额失败:", error);
      // 失败时使用本地计算作为fallback
      this.lastCalculation = this.calculateTotalLocally();
    } finally {
      this.isCalculating = false;
    }
  }

  // 本地计算总金额（作为后端接口的fallback）
  private calculateTotalLocally(): CartCalculation {
    const selectedItems = this.selectedItems;

    let totalQuantity = 0;
    let totalAmount = 0;
    let discountAmount = 0;

    selectedItems.forEach(item => {
      totalQuantity += item.quantity;
      const originalAmount = item.goods_price * item.quantity;
      totalAmount += originalAmount;

      if (item.discount && item.discount < 1) {
        const discountedAmount = originalAmount * item.discount;
        discountAmount += (originalAmount - discountedAmount);
      }
    });

    const finalAmount = totalAmount - discountAmount;

    return {
      totalQuantity,
      totalAmount,
      discountAmount,
      finalAmount,
    };
  }

  // ==================== 公开查询方法 ====================

  // 根据商品编号查找购物车商品
  findItemByGoodsSn(goods_sn: string): ShoppingCartItem | undefined {
    return this.cartItems.find(item => item.goods_sn === goods_sn);
  }

  // 根据条码查找购物车商品
  findItemByBarcode(barcode: string): ShoppingCartItem | undefined {
    return this.cartItems.find(item => item.barcode === barcode);
  }

  // 获取商品在购物车中的数量
  getItemQuantity(barcode: string): number {
    const item = this.findItemByBarcode(barcode);
    return item ? item.quantity : 0;
  }

  // 检查商品是否在购物车中
  isItemInCart(barcode: string): boolean {
    return this.cartItems.some(item => item.barcode === barcode);
  }

  // 获取购物车摘要信息
  getCartSummary(): {
    totalItems: number;
    totalQuantity: number;
    estimatedAmount: number;
  } {
    return {
      totalItems: this.cartItems.length,
      totalQuantity: this.totalQuantity,
      estimatedAmount: this.lastCalculation.finalAmount,
    };
  }

  // 重置store状态
  reset(): void {
    this.cartItems = [];
    this.isCalculating = false;
    this.lastCalculation = {
      totalQuantity: 0,
      totalAmount: 0,
      discountAmount: 0,
      finalAmount: 0,
    };
  }
}

const shoppingBagStore = new ShoppingBagStore();
export default shoppingBagStore;
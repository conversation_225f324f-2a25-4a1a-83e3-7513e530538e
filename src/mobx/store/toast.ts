import { makeAutoObservable } from 'mobx'
import Taro from '@tarojs/taro'

export interface ToastConfig {
  content: string
  icon?: string
  showViewButton?: boolean
  onViewClick?: () => void
  duration?: number
}

class ToastStore {
  constructor() {
    makeAutoObservable(this)
  }
  //标准Toast
  showToast: boolean = false
  toastContent: string = ''
  toastIcon: string = ''
  showViewButton: boolean = false
  onViewClick?: () => void
  duration: number = 3000

  //标准Toast
  show(content: string | ToastConfig) {
    if (typeof content === 'string') {
      // 兼容原有的字符串调用方式，使用Taro原生toast
      Taro.showToast({
        title: content,
        icon: 'none',
        duration: 1000
      })
    } else {
      // 使用新的自定义toast
      this.toastContent = content.content
      this.toastIcon = content.icon || 'success'
      this.showViewButton = content.showViewButton || false
      this.onViewClick = content.onViewClick
      this.duration = content.duration || 3000
      this.showToast = true

      // 自动隐藏
      setTimeout(() => {
        this.hide()
      }, this.duration)
    }
  }

  hide() {
    this.showToast = false
    this.toastContent = ''
    this.toastIcon = ''
    this.showViewButton = false
    this.onViewClick = undefined
  }
}

const toastStore = new ToastStore()

export default toastStore

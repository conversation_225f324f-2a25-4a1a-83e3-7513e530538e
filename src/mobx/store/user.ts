import Taro from "@tarojs/taro";
import {
  UserInfo,
  MicrosoftLoginData,
  BackendMicrosoftAuthResponse
} from "../model/UserInfo";
import { makeAutoObservable } from "mobx";
import http from "@/http";
import { makePersistable } from "mobx-persist-store";
import { md5 } from "js-md5";
import toastStore from "./toast";
import { toJump } from "@/utils";

class UserStore {
  constructor() {
    makeAutoObservable(this);
    makePersistable(this, {
      name: "UserStore",
      properties: ["userInfo"],
      expireIn: 0
    }).then(() => {
      // 持久化数据加载完成后，恢复登录状态
      this.restoreLoginState();
    });
  }

  //是否全局静默登录过一次刷新过身份
  isInitAuthed: boolean = false;
  // 持久化数据是否已加载完成
  isPersistLoaded: boolean = false;
  userInfo: Partial<UserInfo> = {};

  //注册后的跳转页地址
  afterLoggedUrl: string = "";
  afterLoggedCallback: Array<any> = [];

  //重新getToken中发出的请求等待队列
  isRefreshingToken: boolean = false;
  refreshTokenWaitingList: Array<any> = [];

  // 登录页相关参数
  showPasswordErr: boolean = false;

  //是否注册并登录
  get isLogin() {
    return !!this.userInfo?.token;
  }

  // 获取当前登录类型
  get loginType() {
    return this.userInfo?.loginType || "normal";
  }

  // 是否为微软登录
  get isMicrosoftLogin() {
    return this.loginType === "microsoft";
  }

  // 恢复登录状态
  restoreLoginState() {
    try {
      this.isPersistLoaded = true;

      // 如果 store 中没有 token，尝试从本地存储恢复
      if (!this.userInfo?.token) {
        const token = Taro.getStorageSync('token');
        const tokenExpireTime = Taro.getStorageSync('tokenExpireTime');
        const loginType = Taro.getStorageSync('loginType') || 'normal';

        if (token && tokenExpireTime && tokenExpireTime > Date.now()) {
          // Token 有效，恢复登录状态
          this.userInfo = {
            ...this.userInfo,
            token,
            tokenExpireTime,
            loginType
          };
        } else if (token) {
          // Token 过期，清理数据
          this.clearExpiredLoginData();
        }
      } else {
        // Store 中有 token，检查是否过期
        if (this.userInfo.tokenExpireTime && this.userInfo.tokenExpireTime <= Date.now()) {
          this.clearExpiredLoginData();
        }
      }
    } catch (error) {
      console.error('恢复登录状态失败:', error);
      this.isPersistLoaded = true;
    }
  }

  // 清理过期的登录数据
  clearExpiredLoginData() {
    this.userInfo = {};
    Taro.removeStorageSync("token");
    Taro.removeStorageSync("tokenExpireTime");
    Taro.removeStorageSync("loginType");
    this.clearMicrosoftLoginData();
  }

  async loginBtnClick(type, { username, password, code }) {
    // console.log("loginBtnClick", { username, password });
    const params: any = {};
    if (type == "account") {
      if (username == "") {
        toastStore.show({ icon: "error", content: "请输入用户名" });
        return new Promise((_resolve, reject) => {
          reject({ code: "200", status: false });
        });
      }
      if (password == "") {
        toastStore.show({ icon: "error", content: "请输入密码" });
        return new Promise((_resolve, reject) => {
          reject({ code: "200", status: false });
        });
      }
      params.username = md5(username.toUpperCase());
      params.password = md5(password);
    } else {
      params.code = code;
    }

    try {
      
      const req: any = await http.login.login(params);

      if (req.code == 0 && req.data?.access_token) {
        this.getUser(req);
      } else {
        this.userInfo.token = "";
        this.showPasswordErr = true;
        Taro.setStorageSync("token", "");
        toastStore.show({ icon: "error", content: req.msg });
      }
    } catch (e) {

      
      toastStore.show({ icon: "error", content: "网络错误，请稍后再试" });
      console.error(e);
    }
  }
  onChangeUserInfo(info) {
    this.userInfo = info;
  }
  async onLoginByPhone(v) {
    if (!v) return;
    const { code } = v.detail;
    try {
      const req: any = await http.login.loginByPhone({
        code: code
      });

      if (req.code == 0 && req.data?.access_token) {
        this.getUser(req);
      } else {
        this.userInfo.token = "";
        this.showPasswordErr = true;
        Taro.setStorageSync("token", "");
        toastStore.show({ icon: "error", content: req.msg });
      }
    } catch (error) {
      toastStore.show({ icon: "error", content: "网络错误，请稍后再试" });
    }
  }

  async getUser(req) {
    try {
      Taro.setStorageSync("token", req.data?.access_token);
      Taro.setStorageSync(
        "tokenExpireTime",
        parseInt(req.data?.expires) * 1000
      );

      const baseUserInfo: any = await http.common.getDianyuanDetail({
        access_token: req.data?.access_token
      });

      // 保存完整的用户信息到store
      this.userInfo = {
        token: req.data?.access_token,
        tokenExpireTime: req.data?.expires ? req.data?.expires * 1000 : 0,
        loginType: "normal",
        // 将后端返回的所有用户信息都保存到store中
        ...baseUserInfo.data
      };

      toJump("/pages/home/<USER>");
    } catch (error) {

      console.log('-----error',error);
      
    }
  }

  // 微软登录成功处理
  async microsoftLoginSuccess(data: MicrosoftLoginData) {
    // console.log("microsoftLoginSuccess", data);

    try {
      const { authorizationCode, codeVerifier, redirectUri } = data;

      // 临时保存登录状态
      this.userInfo = {
        loginType: "microsoft"
      };

      Taro.setStorageSync("loginType", "microsoft");

      // console.log("收到微软授权码，准备调用后端验证");

      // 调用后端API，传递授权码让后端处理令牌交换和用户信息获取
      const backendResponse = await this.validateMicrosoftAuthCode({
        authorizationCode,
        codeVerifier,
        redirectUri
      });

      // 使用后端返回的用户信息和应用令牌
      this.userInfo = {
        ...this.userInfo,
        token: backendResponse.appToken, // 使用后端返回的应用令牌
        tokenExpireTime: backendResponse.appTokenExpireTime,
        loginType: "microsoft",
        // 将后端返回的所有用户信息都保存到store中
        ...backendResponse
      };

      // 保存应用令牌
      Taro.setStorageSync("token", backendResponse.appToken);
      Taro.setStorageSync(
        "tokenExpireTime",
        backendResponse.appTokenExpireTime
      );

      // console.log("微软登录完成，用户信息已更新:", this.userInfo);
    } catch (error) {
      console.error("微软登录处理失败:", error);
      // 清理临时数据
      this.clearMicrosoftLoginData();
      throw error;
    }
  }

  // 验证微软授权码并获取后端用户信息
  async validateMicrosoftAuthCode(authData: {
    authorizationCode: string;
    codeVerifier: string;
    redirectUri: string;
  }): Promise<BackendMicrosoftAuthResponse> {
    try {
      // 调用后端API来处理微软授权码并获取用户信息
      const response: any = await http.login.validateMicrosoftToken({
        authorizationCode: authData.authorizationCode,
        codeVerifier: authData.codeVerifier,
        redirectUri: authData.redirectUri
      });

      if (response.code === 0) {
        // console.log("微软授权码验证成功:", response.data);
        return response.data as BackendMicrosoftAuthResponse;
      } else {
        throw new Error(response.message || "授权码验证失败");
      }
    } catch (error) {
      console.error("验证微软授权码失败:", error);
      throw error;
    }
  }

  // 验证微软令牌并获取后端用户信息（保留用于刷新令牌等场景）
  async validateMicrosoftToken(
    accessToken: string
  ): Promise<BackendMicrosoftAuthResponse> {
    try {
      // 调用后端API来验证微软令牌并获取用户信息
      const response: any = await http.login.validateMicrosoftToken({
        accessToken: accessToken
      });

      if (response.code === 0) {
        // console.log("微软令牌验证成功:", response.data);
        return response.data as BackendMicrosoftAuthResponse;
      } else {
        throw new Error(response.message || "令牌验证失败");
      }
    } catch (error) {
      console.error("验证微软令牌失败:", error);
      throw error;
    }
  }
  // 清理微软登录临时数据
  clearMicrosoftLoginData() {
    Taro.removeStorageSync("microsoftAccessToken");
    Taro.removeStorageSync("microsoftTokenExpireTime");
    Taro.removeStorageSync("oauth_state");
    Taro.removeStorageSync("oauth_nonce");
    Taro.removeStorageSync("oauth_code_verifier");
    // console.log("微软登录临时数据已清理");
  }

  // 登出方法（扩展以支持微软登录）
  logout() {
    // 清除用户信息
    this.userInfo = {};

    // 清除本地存储
    Taro.removeStorageSync("token");
    Taro.removeStorageSync("tokenExpireTime");
    Taro.removeStorageSync("loginType");

    // 清除微软登录相关数据
    this.clearMicrosoftLoginData();

    // console.log("用户已登出");

    // 跳转到登录页
    Taro.redirectTo({ url: "/pages/login/index" });
  }
}

const userStore = new UserStore();
export default userStore;

import { makeAutoObservable } from "mobx";
import { makePersistable } from "mobx-persist-store";
import i18n, { Language, setLanguage as setI18nLanguage } from "@/i18n";

class I18nStore {
  constructor() {
    makeAutoObservable(this);
    makePersistable(this, {
      name: "I18nStore",
      properties: ["currentLanguage"],
      expireIn: 0,
    }).then(() => {
      // 持久化加载完成后，同步语言设置
      this.syncLanguage();
    });
  }

  // 当前语言
  currentLanguage: Language = 'en';

  // 同步语言设置
  private async syncLanguage() {
    try {
      await setI18nLanguage(this.currentLanguage);
    } catch (error) {
      console.error('Failed to sync language:', error);
    }
  }

  // 设置语言
  async setLanguage(language: Language) {
    this.currentLanguage = language;
    await setI18nLanguage(language);
  }

  // 获取翻译文本
  t(key: string, params?: Record<string, string | number>): string {
    return i18n.t(key, params);
  }

  // 获取当前语言
  getCurrentLanguage(): Language {
    return this.currentLanguage;
  }

  // 获取支持的语言列表
  getSupportedLanguages() {
    return [
      { code: 'en' as Language, name: 'English' },
      // { code: 'zh_cn' as Language, name: '中文' },
    ];
  }

  // 切换语言
  async toggleLanguage() {
    const newLanguage = this.currentLanguage === 'en' ? 'zh_cn' : 'en';
    await this.setLanguage(newLanguage);
  }
}

const i18nStore = new I18nStore();
export default i18nStore;

import Taro from "@tarojs/taro";
import { makeAutoObservable } from "mobx";
import http from "@/http";
import toastStore from "./toast";
import userStore from "./user";

interface Detail {
  uid: string;
  mobile: string; //顾客手机号
  first_name: string;
  last_name: string;
  full_name: string;
  email: string;
  avatar: string; // 暂无
  note: string; // 备注
}
class CustomerDetail {
  constructor() {
    makeAutoObservable(this);
  }
  detail: Partial<Detail> = {};
  async getCustomDetail(mobile) {
    try {
      const res = await http.customer.queryCustomerInfo({
        mobile: mobile,
      });
      this.detail = {
        ...(res.data as any),
        name: res?.data?.first_name + res?.data?.last_name,
      };
    } catch (error) {}
  }
}

const customerDetail = new CustomerDetail();
export default customerDetail;

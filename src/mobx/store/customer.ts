import { makeAutoObservable } from "mobx";
import { makePersistable } from "mobx-persist-store";
import { CustomerInfo } from "../model/UserInfo";
import http from "@/http"; // 暂时注释，等实际API接口实现时再启用
import toastStore from "./toast";

class CustomerStore {
  constructor() {
    makeAutoObservable(this);
    makePersistable(this, {
      name: "CustomerStore",
      properties: ["currentCustomer", "customerList"],
      expireIn: 0,
    }).then();
  }

  // 当前选中的顾客信息
  currentCustomer: Partial<CustomerInfo> = {};

  // 顾客列表（用于当天注册列表等）
  customerList: CustomerInfo[] = [];

  // 当天注册顾客列表
  todayRegisteredCustomers: CustomerInfo[] = [];

  // 是否正在加载当天注册列表
  isLoadingTodayList: boolean = false;

  // 设置当前顾客
  setCurrentCustomer(customer: Partial<CustomerInfo>) {
    // console.log("customer", customer);

    this.currentCustomer = customer;
  }

  // 清空当前顾客
  clearCurrentCustomer() {
    this.currentCustomer = {};
  }

  // 设置顾客列表
  setCustomerList(list: CustomerInfo[]) {
    this.customerList = list;
  }

  // 清空顾客列表
  clearCustomerList() {
    this.customerList = [];
  }

  // 获取顾客详情
  async getCustomerDetail(phone: string) {
    try {
      // 这里需要根据实际的API接口调用
      // const response = await http.customer.getCustomerDetail({ phone })
      // if (response.code === 0) {
      //   this.setCurrentCustomer(response.data)
      //   return response.data
      // } else {
      //   toastStore.show(response.msg || '获取顾客详情失败')
      //   return null
      // }

      // 临时模拟数据，实际使用时请替换为真实API调用
      // console.log("获取顾客详情:", phone);
      const mockCustomer: CustomerInfo = {
        uid: "1231",
        mobile: "67d8eab52ca36",
        first_name: "mia",
        last_name: "mia",
        full_name: "mia",
        email: "",
        avatar: "",
        note: "",
      };
      this.setCurrentCustomer(mockCustomer);
      return mockCustomer;
    } catch (error) {
      console.error("获取顾客详情失败:", error);
      toastStore.show("获取顾客详情失败，请重试");
      return null;
    }
  }

  // 注册新顾客
  async registerCustomer(customerData: CustomerInfo, fn: () => void = () => {}) {
    try {
      // 这里需要根据实际的API接口调用
      const response: any = await http.customer.regist(customerData);
      if (response.code === 0) {
        this.currentCustomer = customerData;
        // 临时模拟数据，实际使用时请替换为真实API调用
        // console.log("注册新顾客:", customerData);
        toastStore.show("顾客注册成功");
        fn();
        return true;
      } else {
        toastStore.show(response.msg || "顾客注册失败");
        return false;
      }
    } catch (error) {
      console.error("注册顾客失败:", error);
      toastStore.show("注册顾客失败，请重试");
      return false;
    }
  }

  // 更新顾客信息
  async updateCustomer(customerData: { uid: string; phone?: string; email?: string; first_name?: string; last_name?: string; note?: string }, fn: () => void = () => {}) {
    try {
      const response: any = await http.customer.updateMemberInfo(customerData);
      if (response.code === 0) {
        // 更新当前客户信息
        this.currentCustomer = { ...this.currentCustomer, ...customerData };
        // console.log("更新顾客信息:", customerData);
        toastStore.show("顾客信息更新成功");
        fn();
        return true;
      } else {
        toastStore.show(response.msg || "顾客信息更新失败");
        return false;
      }
    } catch (error) {
      console.error("更新顾客信息失败:", error);
      toastStore.show("更新顾客信息失败，请重试");
      return false;
    }
  }

  // 更新顾客邮件推送设置
  async updateCustomerEmailSettings(
    customerId: string,
    settings: {
      newsletter: string;
      digitalReceipt: string;
    }
  ) {
    try {
      // 这里需要根据实际的API接口调用
      // const response = await http.customer.updateEmailSettings({
      //   id: customerId,
      //   ...settings
      // })
      // if (response.code === 0) {
      //   toastStore.show('设置更新成功')
      //   return true
      // } else {
      //   toastStore.show(response.msg || '设置更新失败')
      //   return false
      // }

      // 临时模拟数据，实际使用时请替换为真实API调用
      // console.log("更新顾客邮件设置:", customerId, settings);
      toastStore.show("设置更新成功");
      return true;
    } catch (error) {
      console.error("更新顾客邮件设置失败:", error);
      toastStore.show("设置更新失败，请重试");
      return false;
    }
  }

  // 切换到访客模式
  switchGuest(fn = () => {}) {
    // 生成3位随机数
    const randomNum = Math.floor(Math.random() * 900) + 100; // 100-999之间的随机数
    const guestName = `guest${randomNum}`;
    const guestCustomer: Partial<CustomerInfo> = {
      uid: "guest",
      mobile: "",
      first_name: guestName,
      last_name: "",
      full_name: guestName,
      email: "",
      avatar: "",
      note: "Guest customer",
    };

    this.setCurrentCustomer(guestCustomer);
    // console.log("切换到访客模式:", guestCustomer);
    toastStore.show(`欢迎访客 ${guestName}`);
    fn();
  }

  // 重置store状态
  reset() {
    this.currentCustomer = {};
    this.customerList = [];
    this.todayRegisteredCustomers = [];
    this.isLoadingTodayList = false;
  }
}

const customerStore = new CustomerStore();
export default customerStore;

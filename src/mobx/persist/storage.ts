import Taro from '@tarojs/taro'
export default {
  getItem(key) {
    return Taro.getStorage({ key }).then(
      (res) => {
        return res.data
      },
      (_err) => {
        //大概率是没找到数据 不要抛错
        // console.log(_err)
        return
      },
    )
  },

  setItem(key, data) {
    return Taro.setStorage({ key, data })
  },

  removeItem(key) {
    return Taro.removeStorage({ key })
  },

  clear() {
    return Taro.clearStorage()
  },
}

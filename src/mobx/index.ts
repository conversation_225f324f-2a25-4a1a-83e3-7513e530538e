//全局配置
//不可以直接写这里 否则store的构造函数会先执行
import './config'

export { default as commonStore } from './store/common'
export { default as loadingStore } from './store/loading'
export { default as toastStore } from './store/toast'
export { default as userStore } from './store/user'
export { default as customerStore } from './store/customer'
export { default as settingStore } from './store/setting'
export { default as i18nStore } from './store/i18n'
export { default as customerDetail } from './store/customerDetail'
export { default as shoppingBagStore } from './store/mall'


export interface OrderGoods {
  goods_sn: string;
  goods_name: string;
  goods_img: string;
  goods_price: number;
  discription: string;
  color: string;
  serial_no: string;
  size: string;
  barcode: string;
  status: string;
  detail: {
    MasterProductId: string;
    Material: string;
  };
  detail_img: string[];
  cm_url: string;
  alteration_url: string;
}

export interface OrderItem {
  order_sn: string;
  order_time: string;
  order_status: string;
  order_price: number;
  order_qty: number;
  custom: string;
  goods: OrderGoods[];
  [key: string]: any;
}

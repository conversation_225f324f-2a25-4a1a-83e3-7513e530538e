// 用户信息接口
export interface UserInfo {
  username?: string
  password?: string
  remember?: boolean
  token?: string
  tokenExpireTime?: number

  // 微软登录相关字段
  microsoftAccessToken?: string
  microsoftRefreshToken?: string
  microsoftTokenExpireTime?: number
  loginType?: 'normal' | 'microsoft' // 登录类型

  // 微软用户信息
  microsoftUserInfo?: MicrosoftUserInfo

  // 通用用户信息字段（可以存储后端返回的任何用户数据）
  [key: string]: any
}

// 微软用户信息接口
export interface MicrosoftUserInfo {
  id?: string
  displayName?: string
  givenName?: string
  surname?: string
  userPrincipalName?: string
  mail?: string
  mobilePhone?: string
  jobTitle?: string
  officeLocation?: string
  preferredLanguage?: string
}

// 微软登录成功后的数据接口（前端只处理授权码）
export interface MicrosoftLoginData {
  authorizationCode: string
  codeVerifier: string
  redirectUri: string
}

// 后端验证微软令牌的响应接口
export interface BackendMicrosoftAuthResponse {
  appToken: string
  appTokenExpireTime: number
  userInfo: {
    username: string
    [key: string]: any // 其他用户信息由后端定义
  }
}

// 顾客信息接口
export interface CustomerInfo {
  uid?: string // 顾客ID
  mobile: string // 顾客手机号
  first_name: string // 名
  last_name: string // 姓
  full_name?: string // 全名
  email?: string // 邮箱
  avatar?: string // 头像 (暂无)
  note?: string // 备注 (暂无，需要和crm沟通)
  // 扩展字段，用于存储其他可能的顾客信息
  [key: string]: any
}

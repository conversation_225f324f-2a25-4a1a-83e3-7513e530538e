/** @type {import('tailwindcss').Config} */
module.exports = {
  content: ["./src/**/*.{js,jsx,ts,tsx}", "./src/**/*.{html,vue}"],
  theme: {
    // 基于 375px 设计稿的尺寸系统 (启用 pxtransform 后会自动转换为 rpx)
    spacing: {
      0: "0px",
      0.5: "1px", // 2rpx
      1: "2px", // 4rpx
      1.5: "3px", // 6rpx
      2: "4px", // 8rpx
      2.5: "5px", // 10rpx
      3: "6px", // 12rpx
      3.5: "7px", // 14rpx
      4: "8px", // 16rpx
      5: "10px", // 20rpx
      6: "12px", // 24rpx
      7: "14px", // 28rpx
      8: "16px", // 32rpx
      9: "18px", // 36rpx
      10: "20px", // 40rpx
      11: "22px", // 44rpx
      12: "24px", // 48rpx
      14: "28px", // 56rpx
      16: "32px", // 64rpx
      17: "34px", // 68rpx
      18: "36px", // 72rpx
      20: "40px", // 80rpx
      22: "44px", // 88rpx
      23: "46px", // 88rpx
      24: "48px", // 96rpx
      26: "52px", // 104rpx
      28: "56px", // 112rpx
      30: "60px", // 120rpx
      32: "64px", // 128rpx
      36: "72px", // 144rpx
      38: "76px", // 144rpx
      40: "80px", // 160rpx
      42: "84px", // 168rpx
      43: "86px", // 168rpx
      44: "88px", // 176rpx
      48: "96px", // 192rpx
      50: "100px", // 192rpx
      51: "102px", // 192rpx
      52: "104px", // 208rpx
      56: "112px", // 224rpx
      60: "120px", // 240rpx
      64: "128px", // 256rpx
      67: "134px", // 256rpx
      68: "136px", // 256rpx
      72: "144px", // 288rpx
      80: "160px", // 320rpx
      92: "184px", // 320rpx
      93: "186px", // 320rpx

      96: "192px", // 384rpx
      100: "200px", // 400rpx
      111: "222px", // 400rpx
      112: "224px", // 400rpx
      130: "260px", // 400rpx
      135: "270px", // 400rpx
      182: "364px", // 384rpx
    },
    leading: {
      0: "0px",
      1: "2px", // 4rpx
      1.5: "3px", // 6rpx
      2: "4px", // 8rpx
      2.5: "5px", // 10rpx
      3: "6px", // 12rpx
      3.5: "7px", // 14rpx
      4: "8px", // 16rpx
      5: "10px", // 20rpx
      6: "12px", // 24rpx
      7: "14px", // 28rpx
      8: "16px", // 32rpx
      9: "18px", // 36rpx
      10: "20px", // 40rpx
    },
    fontSize: {
      xs: "10px", // 20rpx - 超小字体
      sm: "12px", // 24rpx - 小字体
      base: "14px", // 28rpx - 基础字体
      md: "16px", // 32rpx - 中等字体
      lg: "18px", // 36rpx - 大字体
      xl: "20px", // 40rpx - 超大字体
      "2xl": "24px", // 48rpx - 标题字体
      "3xl": "28px", // 56rpx - 大标题
      "4xl": "32px", // 64rpx - 超大标题
      "5xl": "36px", // 72rpx - 巨大标题
      "6xl": "40px", // 80rpx - 超巨大标题
    },

    lineHeight: {
      5: "10px", // 20rpx
      6: "12px", // 24rpx
      7: "14px", // 28rpx
      8: "16px", // 32rpx
      9: "18px", // 36rpx
      10: "20px", // 40rpx
      11: "22px", // 44rpx
      12: "24px", // 48rpx
      14: "28px", // 56rpx
      16: "32px", // 64rpx
      18: "36px", // 72rpx
      20: "40px", // 80rpx
      22: "22px", // 44rpx
      24: "48px", // 96rpx
      28: "56px", // 112rpx
      32: "64px", // 128rpx
      36: "72px", // 144rpx
      40: "80px", // 160rpx
      44: "88px", // 176rpx
      48: "96px", // 192rpx
      50: "100px", // 192rpx
      52: "104px", // 208rpx
      56: "112px", // 224rpx
      60: "120px", // 240rpx
      64: "128px", // 256rpx
      72: "144px", // 288rpx
      80: "160px", // 320rpx
      96: "192px", // 384rpx
    },

    extend: {
      // 小程序常用的颜色 - 只添加自定义颜色，保留 Tailwind 默认颜色
      colors: {
        primary: {
          50: "#eff6ff",
          100: "#dbeafe",
          200: "#bfdbfe",
          300: "#93c5fd",
          400: "#60a5fa",
          500: "#3b82f6",
          600: "#2563eb",
          700: "#1d4ed8",
          800: "#1e40af",
          900: "#1e3a8a",
        },
        // 项目特定的颜色
        brand: {
          C33333: "#C33333",
          "2D2E2C": "#2D2E2C",
          898989: "#898989",
          "000000": "#000000",
          E1E1E1: "#E1E1E1",
          FAFAFA: "#FAFAFA",
          707070: "#707070",
          "8C8C8C": "#8C8C8C",
          "45A600": "#45A600",
          999999: "#999999",
          E5E5E5: "#E5E5E5",
          EFEFEF: "#EFEFEF",
          BABABA: "#BABABA",
          F6F6F6: "#F6F6F6",
        },
      },
      borderRadius: {
        default: "4px",
        14: "14px",
        15: "15px",
        8: "8px",
        4: "4px",
        24: "24px",
        50: "50px",
      },
      height: {
        288: "288px",
        238: "238px",
        94: "94px",
      },
      width: {
        214: "214px",
        70: "70px",
      },
      minWidth: {
        214: "214px",
      },
    },
  },
  plugins: [],
  // 针对小程序环境的配置
  corePlugins: {
    // 禁用一些在小程序中不支持的功能
    preflight: false, // 禁用默认样式重置，避免影响小程序原生组件
    container: false, // 禁用容器类
    accessibility: false, // 禁用无障碍功能
    backdropBlur: false, // 禁用背景模糊
    backdropBrightness: false,
    backdropContrast: false,
    backdropGrayscale: false,
    backdropHueRotate: false,
    backdropInvert: false,
    backdropOpacity: false,
    backdropSaturate: false,
    backdropSepia: false,
    blur: false, // 禁用模糊效果
    brightness: false,
    contrast: false,
    dropShadow: false, // 禁用投影
    filter: false, // 禁用滤镜
    grayscale: false,
    hueRotate: false,
    invert: false,
    saturate: false,
    sepia: false,
    // 禁用使用 CSS 变量的功能
    space: false, // 禁用 space-x 和 space-y 类
    divideWidth: false, // 禁用 divide 类
    divideColor: false,
    divideOpacity: false,
    divideStyle: false,
    // 禁用visibility相关类，避免生成带反斜杠的类名
    visibility: false,
    // 保留基础功能
    // display: true,
    // flexbox: true,
    // grid: true,
    // spacing: true,
    // sizing: true,
    // typography: true,
    // backgroundColor: true,
    // textColor: true,
    // borderColor: true,
    // borderRadius: true,
    // borderWidth: true,
    // margin: true,
    // padding: true,
  },
};

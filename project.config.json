{"miniprogramRoot": "dist/", "projectname": "pos_suitsupply", "description": "", "appid": "wx2b8bdf392fc19e3f", "setting": {"urlCheck": true, "es6": true, "enhance": true, "compileHotReLoad": false, "postcss": false, "minified": true, "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "packNpmRelationList": [], "compileWorklet": false, "uglifyFileName": false, "uploadWithSourceMap": true, "packNpmManually": false, "minifyWXSS": true, "minifyWXML": true, "localPlugins": false, "disableUseStrict": false, "useCompilerPlugins": false, "condition": false, "swc": false, "disableSWC": true}, "compileType": "miniprogram", "libVersion": "3.8.10", "srcMiniprogramRoot": "dist/", "packOptions": {"ignore": [], "include": []}, "condition": {}, "editorSetting": {"tabIndent": "auto", "tabSize": 2}, "simulatorPluginLibVersion": {}}
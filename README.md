pos 小程序

##### git 添加 ignore

- git rm -r --cached 文件夹
- git rm --cached 文件
- git commit -m "Remove tracked files and update .gitignore"
- git push origin develop

iconfont-taro 更新命令

npx iconfont-taro

# MainLayout 中的 HeaderBar 配置指南

````tsx
import MainLayout from '@/components/MainLayout'

// 1. 基础 SUITSUPPLY 头部
<HeaderBar type="basic" headerTitle="SUITSUPPLY" />

// 2. 带返回按钮的头部
<HeaderBar type="withBack" headerTitle="SUITSUPPLY" />

// 3. 带返回和用户图标的头部
<HeaderBar
  type="withBackIcon"
  headerTitle="Selected customers"
  leftIcon={<User size="20px" />}
/>

// 4. 带返回和菜单的头部
<HeaderBar type="withBackMenu" headerTitle="SUITSUPPLY" />

// 5. 自定义样式的头部
<HeaderBar
  type="withBack"
  headerTitle="Customer profile"
  backgroundColor="#9C27B0"
  color="#fff"
/>
```



## 屏幕尺寸适配

### 1. 优先使用Tailwind CSS

```tsx
// 推荐：使用Tailwind类名
<View className="flex items-center justify-between p-4 bg-white rounded-lg">
  <Text className="text-base font-medium">标题</Text>
  <Text className="text-sm text-gray-500">副标题</Text>
</View>
```

### 2. 复杂布局使用响应式工具
```tsx
// 适合：动态计算的复杂样式
const dynamicStyle = responsiveStyle({
  width: screenWidth * 0.8,  // 屏幕宽度的80%
  height: itemHeight,        // 动态高度
  marginTop: headerHeight,   // 基于头部高度的边距
})
```

### 3. 避免硬编码px值

```tsx
// 推荐：使用Tailwind或响应式工具
<View className="w-48 h-24">
// 或
<View style={{ width: rpx(200), height: rpx(100) }}>
```

### 4. 合理使用断点
```css
// 小屏幕适配
@media (max-width: 480px) {
  .container {
    padding: 12px;
  }
}

// 大屏幕适配
@media (min-width: 769px) {
  .container {
    max-width: 768px;
    margin: 0 auto;
  }
}
```



#### loading组件
## 注意事项
1. **唯一标识符**：每次调用 `open()` 都会返回一个唯一的 UUID，必须使用这个 UUID 来关闭对应的 Loading
2. **页面路径检测**：Loading 组件会自动检测当前页面路径，确保只在当前页面显示
3. **性能优化**：隐藏时使用 `opacity: 0` 而不是移除 DOM，避免重绘导致的闪烁
4. **多实例支持**：支持同时打开多个 Loading，会自动管理显示状态
5. **自动清理**：页面卸载时会自动清理相关的 Loading 状态

## 最佳实践

1. **快速接口**：对于预期在 500ms 内完成的接口，建议设置 `showDelay: 500`
2. **初始化场景**：页面初始化时使用 `isInitLoading: true` 和 `isFullScreen: true`
3. **错误处理**：在 catch 块中确保调用 `loadingStore.close(uuid)`
4. **组件卸载**：在组件卸载时清理未关闭的 Loading

```tsx
useEffect(() => {
  return () => {
    // 组件卸载时清理
    loadingStore.close(loadingUUID)
  }
}, [])


// 基础使用
import { useStores } from '@/hook'
const MyComponent = () => {
  const { loadingStore } = useStores()

  const handleClick = () => {
    // 显示普通 Loading
    const uuid = loadingStore.open()

    // 执行异步操作
    setTimeout(() => {
      loadingStore.close(uuid)
    }, 2000)
  }

  return <Button onClick={handleClick}>显示Loading</Button>
}


### 2. 全屏 Loading

const showFullScreenLoading = () => {
  const uuid = loadingStore.openFullScreen()

  // 或者使用 open 方法
  // const uuid = loadingStore.open({ isFullScreen: true })

  setTimeout(() => {
    loadingStore.close(uuid)
  }, 2000)
}


### 3. 延迟显示 Loading

避免快速接口导致的闪烁效果：


const showDelayedLoading = () => {
  const uuid = loadingStore.open({
    showDelay: 500 // 500ms 后才显示 Loading
  })

  setTimeout(() => {
    loadingStore.close(uuid)
  }, 2000)
}

### 4. 初始化 Loading

用于页面初始化时的加载状态：

const showInitLoading = () => {
  const uuid = loadingStore.open({
    isInitLoading: true,
    isFullScreen: true
  })

  // 初始化完成后关闭
  setTimeout(() => {
    loadingStore.close(uuid)
  }, 2000)
}

### 5. 在 MainLayout 中使用初始化选项

<MainLayout
  initOptions={{
    inited: false,        // 是否已初始化完成
    initLoading: true,    // 是否显示初始化 Loading
    loadingFullScreen: true, // 是否全屏显示
    initDelay: 1000      // 初始化完成后延迟多久隐藏
  }}
>
  {/* 页面内容 */}
</MainLayout>

```

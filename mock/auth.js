// 认证相关的 Mock 数据
module.exports = {
  // 用户登录
  "POST /web/mobile/auth.php": (req, res) => {
    const { username, password } = req.body;
    console.log("username && password", username && password);

    if (username && password) {
      res.json({
        code: 200,
        message: "登录成功",
        data: {
          access_token: "mock_access_token_" + Date.now(),
          expires: Math.floor(Date.now() / 1000) + 3600 // 1小时后过期
        }
      });
    } else {
      res.status(401).json({
        code: 401,
        message: "用户名或密码错误"
      });
    }
  },

  // 手机号码快捷登录
  "POST /web/mobile/phone-auth.php": (req, res) => {
    const { code } = req.body;
    console.log("code", code);
    if (code) {
      res.json({
        code: 0,
        message: "登录成功",
        data: {
          access_token: "mock_access_token_" + Date.now(),
          expires: Math.floor(Date.now() / 1000) + 3600 // 1小时后过期
        }
      });
    } else {
      res.status(401).json({
        code: 401,
        message: "用户名或密码错误"
      });
    }
  },

  // 微软登录验证
  "POST /web/mobile/microsoft-auth.php": (req, res) => {
    const { access_token } = req.body;

    if (access_token) {
      res.json({
        code: 200,
        message: "微软登录验证成功",
        data: {
          access_token: "mock_microsoft_access_token_" + Date.now(),
          expires: Math.floor(Date.now() / 1000) + 3600
        }
      });
    } else {
      res.status(401).json({
        code: 401,
        message: "微软令牌验证失败"
      });
    }
  }
};

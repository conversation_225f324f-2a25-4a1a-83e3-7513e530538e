// 通用接口的 Mock 数据
module.exports = {
  // 顾客信息
  "POST /web/mobile/default.php?c=register": (req, res) => {
    const { access_token } = req.body;

    // if (access_token) {
    res.json({
      code: 0,
      message: "注册成功",
      data: {}
    });
    // } else {
    // 	res.status(401).json({
    // 		code: 401,
    // 		message: '/web/mobile/default.php?c=register'
    // 	});
    // }
  },
  // 模糊搜索
  "POST /web/mobile/default.php?c=searchCustomers": (req, res) => {
    const { access_token } = req.body;

    // if (access_token) {
    res.json({
      code: 0,
      message: "注册成功",
      data: [
        {
          uid: "1231",
          mobile: "13555555555",
          first_name: "sky",
          last_name: "sky",
          full_name: "sky",
          email: "",
          avatar: "",
          note: ""
        },
        {
          uid: "1231",
          mobile: "13555555554",
          first_name: "mia",
          last_name: "mia",
          full_name: "mia",
          email: "",
          avatar: "",
          note: ""
        },
        {
          uid: "1231",
          mobile: "13888885555",
          first_name: "guo",
          last_name: "erio",
          full_name: "Erio Guo",
          email: "<EMAIL>",
          avatar: "",
          note: "这个是note"
        }
      ]
    });
    // } else {
    // 	res.status(401).json({
    // 		code: 401,
    // 		message: '/web/mobile/default.php?c=register'
    // 	});
    // }
  },

  // ==================== Address 地址相关接口 ====================

  // 获取省份列表
  "GET /web/mobile/default.php?c=address&m=province_list": (req, res) => {
    res.json({
      code: 0,
      data: [
        { id: 1, name: '北京市' },
        { id: 2, name: '上海市' },
        { id: 3, name: '广东省' },
        { id: 4, name: '浙江省' },
        { id: 5, name: '江苏省' },
        { id: 6, name: '山东省' },
        { id: 7, name: '河北省' },
        { id: 8, name: '河南省' },
        { id: 9, name: '湖北省' },
        { id: 10, name: '湖南省' }
      ]
    });
  },

  // 获取城市列表
  "GET /web/mobile/default.php?c=address&m=city_list": (req, res) => {
    const { province_id } = req.query;
    const cityData = {
      1: [{ id: 101, name: '北京市', province_id: 1 }],
      2: [{ id: 201, name: '上海市', province_id: 2 }],
      3: [
        { id: 301, name: '广州市', province_id: 3 },
        { id: 302, name: '深圳市', province_id: 3 },
        { id: 303, name: '珠海市', province_id: 3 },
        { id: 304, name: '东莞市', province_id: 3 }
      ],
      4: [
        { id: 401, name: '杭州市', province_id: 4 },
        { id: 402, name: '宁波市', province_id: 4 },
        { id: 403, name: '温州市', province_id: 4 }
      ],
      5: [
        { id: 501, name: '南京市', province_id: 5 },
        { id: 502, name: '苏州市', province_id: 5 },
        { id: 503, name: '无锡市', province_id: 5 }
      ]
    };

    res.json({
      code: 0,
      data: cityData[province_id] || []
    });
  },

  // 获取区县列表
  "GET /web/mobile/default.php?c=address&m=district_list": (req, res) => {
    const { city_id } = req.query;
    const districtData = {
      101: [
        { id: 10101, name: '东城区', city_id: 101 },
        { id: 10102, name: '西城区', city_id: 101 },
        { id: 10103, name: '朝阳区', city_id: 101 },
        { id: 10104, name: '海淀区', city_id: 101 }
      ],
      201: [
        { id: 20101, name: '黄浦区', city_id: 201 },
        { id: 20102, name: '徐汇区', city_id: 201 },
        { id: 20103, name: '长宁区', city_id: 201 },
        { id: 20104, name: '静安区', city_id: 201 }
      ],
      301: [
        { id: 30101, name: '天河区', city_id: 301 },
        { id: 30102, name: '越秀区', city_id: 301 },
        { id: 30103, name: '海珠区', city_id: 301 }
      ],
      302: [
        { id: 30201, name: '福田区', city_id: 302 },
        { id: 30202, name: '南山区', city_id: 302 },
        { id: 30203, name: '宝安区', city_id: 302 }
      ]
    };

    res.json({
      code: 0,
      data: districtData[city_id] || []
    });
  },

};

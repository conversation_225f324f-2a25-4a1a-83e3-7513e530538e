// 通用接口的 Mock 数据
module.exports = {
  // 支付终端列表
  "POST /web/mobile/default.php?c=payment_terminal&m=list": (req, res) => {
    res.json({
      code: 0,
      msg: "成功",
      data: [
        {
          id: "1",
          name: "设备1"
        },
        {
          id: "2",
          name: "设备2"
        }
      ]
    });
  },

  "POST /web/mobile/default.php?c=store&m=list": (req, res) => {
    res.json({
      code: 0,
      msg: "成功",
      data: [
        {
          id: "1",
          name: "店铺1"
        },
        {
          id: "2",
          name: "店铺2"
        },
        {
          id: "3",
          name: "店铺3"
        }
      ]
    });
  },

  "POST /web/mobile/default.php?c=user&m=setting_save": (req, res) => {
    res.json({
      code: 0,
      msg: "成功",
      data: []
    });
  }
};
